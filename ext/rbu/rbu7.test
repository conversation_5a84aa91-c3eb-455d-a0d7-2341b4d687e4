# 2014 October 21
#
# The author disclaims copyright to this source code.  In place of
# a legal notice, here is a blessing:
#
#    May you do good and not evil.
#    May you find forgiveness for yourself and forgive others.
#    May you share freely, never taking more than you give.
#
#***********************************************************************
#
# This file contains tests for the RBU module.
#


source [file join [file dirname [info script]] rbu_common.tcl]
if_no_rbu_support { finish_test ; return }
set ::testprefix rbu7

# Test index:
#
#   1.*: That affinities are correctly applied to values within the 
#        RBU database.
#
#   2.*: Tests for multi-column primary keys.
#

do_test 1.0 {
  execsql {
    CREATE TABLE t1(a INT PRIMARY KEY, b) WITHOUT ROWID;
    INSERT INTO t1 VALUES(1, 'abc');
    INSERT INTO t1 VALUES(2, 'def');
  }

  forcedelete rbu.db
  sqlite3 rbu rbu.db
  rbu eval {
    CREATE TABLE data_t1(a, b, rbu_control);
    INSERT INTO data_t1 VALUES('1', NULL, 1);
  }
  rbu close
} {}

do_test 1.1 {
  sqlite3rbu rbu test.db rbu.db
  while { [rbu step]=="SQLITE_OK" } {}
  rbu close
} {SQLITE_DONE}

sqlite3 db test.db
do_execsql_test 1.2 {
  SELECT * FROM t1
} {2 def}

#-------------------------------------------------------------------------
#
foreach {tn tbl} {
  1 { CREATE TABLE t1(a, b, c, PRIMARY KEY(a, b)) WITHOUT ROWID }
  2 { CREATE TABLE t1(a, b, c, PRIMARY KEY(a, b)) }
} {
  reset_db

  execsql $tbl
  do_execsql_test 2.$tn.1 {
    CREATE INDEX t1c ON t1(c);
    INSERT INTO t1 VALUES(1, 1, 'a');
    INSERT INTO t1 VALUES(1, 2, 'b');
    INSERT INTO t1 VALUES(2, 1, 'c');
    INSERT INTO t1 VALUES(2, 2, 'd');
  }

  do_test 2.$tn.2 {
    forcedelete rbu.db
    sqlite3 rbu rbu.db
    execsql {
      CREATE TABLE data_t1(a, b, c, rbu_control);
      INSERT INTO data_t1 VALUES(3, 1, 'e', 0);
      INSERT INTO data_t1 VALUES(3, 2, 'f', 0);
      INSERT INTO data_t1 VALUES(1, 2, NULL, 1);
      INSERT INTO data_t1 VALUES(2, 1, 'X', '..x');
    } rbu
    rbu close
  } {}

  do_test 2.$tn.3 {
    set rc "SQLITE_OK"
    while {$rc == "SQLITE_OK"} {
      sqlite3rbu rbu test.db rbu.db
      rbu step
      set rc [rbu close]
    } 
    set rc
  } {SQLITE_DONE}

  do_execsql_test 2.$tn.1 {
    SELECT * FROM t1 ORDER BY a, b
  } {
    1 1 a
    2 1 X
    2 2 d
    3 1 e
    3 2 f
  }
}

finish_test
