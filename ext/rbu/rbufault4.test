# 2014 October 22
#
# The author disclaims copyright to this source code.  In place of
# a legal notice, here is a blessing:
#
#    May you do good and not evil.
#    May you find forgiveness for yourself and forgive others.
#    May you share freely, never taking more than you give.
#
#***********************************************************************
#

source [file join [file dirname [info script]] rbu_common.tcl]
if_no_rbu_support { finish_test ; return }
source $testdir/malloc_common.tcl
set ::testprefix rbufault4

for {set tn 1} {1} {incr tn} {
  reset_db
  do_execsql_test 1.0 {
    CREATE TABLE t1(a INTEGER PRIMARY KEY, b, c);
    CREATE INDEX i1b ON t1(b);
    CREATE INDEX i1c ON t1(c);
    INSERT INTO t1 VALUES(1, 2, 3);
    INSERT INTO t1 VALUES(4, 5, 6);
  }
  db close

  forcedelete test.db2
  sqlite3rbu_vacuum rbu test.db test.db2
  for {set i 0} {$i < $tn} {incr i} { rbu step }
  set rc [rbu close]
  if {$rc!="SQLITE_OK"} { 
    if {$rc!="SQLITE_DONE"} {error $rc}
    break
  }
  faultsim_save

  do_faultsim_test $tn -faults oom-t* -prep {
    faultsim_restore
  } -body {
    sqlite3rbu_vacuum rbu test.db test.db2
    while 1 {
      set rc [rbu step]
      if {$rc=="SQLITE_DONE"} break
      if {$rc!="SQLITE_OK"} { error $rc }
    }
  } -test {
    catch {rbu close}
    faultsim_test_result {0 {}} {1 SQLITE_NOMEM} {1 SQLITE_IOERR_NOMEM}

    sqlite3rbu_vacuum rbu test.db test.db2
    while {[rbu step]=="SQLITE_OK"} {}
    set trc [rbu close]
    if {$trc!="SQLITE_DONE"} { error "Got $trc instead of SQLITE_DONE!" }

    sqlite3 db test.db
    set rc [db one {PRAGMA integrity_check}]
    if {$rc!="ok"} { error "Got $rc instead of ok!" }
    db close
  }
}



finish_test
