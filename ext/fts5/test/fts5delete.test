# 2017 May 12
#
# The author disclaims copyright to this source code.  In place of
# a legal notice, here is a blessing:
#
#    May you do good and not evil.
#    May you find forgiveness for yourself and forgive others.
#    May you share freely, never taking more than you give.
#
#*************************************************************************
# This file implements regression tests for SQLite library.  The
# focus of this script is testing the FTS5 module.
#

source [file join [file dirname [info script]] fts5_common.tcl]
set testprefix fts5delete

# If SQLITE_ENABLE_FTS5 is not defined, omit this file.
ifcapable !fts5 {
  finish_test
  return
}
fts5_aux_test_functions db

do_execsql_test 1.0 {
  CREATE VIRTUAL TABLE t1 USING fts5(x);
  WITH s(i) AS (
    SELECT 1 UNION ALL SELECT i+1 FROM s WHERE i<5000
  )
  INSERT INTO t1(rowid, x) SELECT i, (i/2)*2 FROM s;
}

do_test 1.1 {
  execsql BEGIN
  for {set i 1} {$i<=5000} {incr i} {
    if {$i % 2} {
      execsql { INSERT INTO t1 VALUES($i) }
    } else {
      execsql { DELETE FROM t1 WHERE rowid = $i }
    }
  }
  execsql COMMIT
} {}

do_test 1.2 {
  execsql { INSERT INTO t1(t1, rank) VALUES('usermerge', 2); }
  for {set i 0} {$i < 5} {incr i} {
    execsql { INSERT INTO t1(t1, rank) VALUES('merge', 1) }
    execsql { INSERT INTO t1(t1) VALUES('integrity-check') }
  }
} {}

#-------------------------------------------------------------------------
reset_db
do_execsql_test 2.0 {
  CREATE TABLE test (
      id INTEGER PRIMARY KEY,
      name TEXT,
      value TEXT
  );
  CREATE VIRTUAL TABLE test_idx USING fts5(
      name, content=test, content_rowid=id
  );
}

do_catchsql_test 2.1 {
  INSERT INTO test_idx (test_idx, rowid, name) VALUES('delete', 1, 'quick');
} {1 {database disk image is malformed}}

do_catchsql_test 2.2 {
  INSERT INTO test_idx(rowid, name) VALUES(123, 'one one one');
  INSERT INTO test_idx (test_idx, rowid, name) VALUES('delete', 123, 'one');
  INSERT INTO test_idx (test_idx, rowid, name) VALUES('delete', 123, 'one');
} {1 {database disk image is malformed}}

do_execsql_test 2.3 {
  DROP TABLE test_idx;
  CREATE VIRTUAL TABLE test_idx USING fts5(
      name, content=test, content_rowid=id
  );

  INSERT INTO test_idx(rowid, name) VALUES(123, 'one one one');
  INSERT INTO test_idx(rowid, name) VALUES(124, 'two two two');
  INSERT INTO test_idx(rowid, name) VALUES(125, 'two two two');
  INSERT INTO test_idx (test_idx, rowid, name) VALUES('delete', 123, 'one');
  INSERT INTO test_idx (test_idx, rowid, name) VALUES('delete', 123, 'one');
  INSERT INTO test_idx (test_idx, rowid, name) VALUES('delete', 123, 'one');
}

do_catchsql_test 2.4 {
  SELECT rowid FROM test_idx WHERE test_idx MATCH 'two' ORDER BY rank;
} {1 {database disk image is malformed}}

#-------------------------------------------------------------------------
reset_db
do_execsql_test 3.0 {
  CREATE VIRTUAL TABLE tx USING fts5(a, b, c, d, content=);
  INSERT INTO tx(rowid, a, c) VALUES(1, 'abc def', 'a b c');
  INSERT INTO tx(rowid, a, c) VALUES(5, 'a b c', 'a b d def');
}
do_execsql_test 3.1 {
  INSERT INTO tx(tx, rowid, a, b, c, d) 
    VALUES('delete', 5, 'a b c', NULL, 'a b d def', NULL);
}
do_execsql_test 3.2 {
  INSERT INTO tx(tx) VALUES('integrity-check');
}
do_execsql_test 3.3 {
  INSERT INTO tx(tx, rowid, a, b, c, d) 
    VALUES('delete', 1, 'abc def', NULL, 'a b c', NULL);
}
do_execsql_test 3.4 {
  INSERT INTO tx(tx) VALUES('integrity-check');
}

#-------------------------------------------------------------------------
reset_db
do_execsql_test 4.0 {
  CREATE VIRTUAL TABLE ft1 USING fts5(a, b UNINDEXED, 
      content='', contentless_unindexed=1
  );
  CREATE VIRTUAL TABLE ft2 USING fts5(a, b UNINDEXED, 
      content='', contentless_unindexed=1, contentless_delete=1
  );

  INSERT INTO ft1(rowid, a, b) VALUES(1, 'one', 'i');
  INSERT INTO ft1(rowid, a, b) VALUES(2, 'two', 'ii');
  INSERT INTO ft1(rowid, a, b) VALUES(3, 'three', 'iii');
  INSERT INTO ft2(rowid, a, b) VALUES(1, 'one', 'i');
  INSERT INTO ft2(rowid, a, b) VALUES(2, 'two', 'ii');
  INSERT INTO ft2(rowid, a, b) VALUES(3, 'three', 'iii');
}

do_catchsql_test 4.1 {
  DELETE FROM ft1 WHERE rowid=2
} {1 {cannot DELETE from contentless fts5 table: ft1}}
do_catchsql_test 4.2 {
  DELETE FROM ft2 WHERE rowid=2
} {0 {}}

do_catchsql_test 4.3 {
  INSERT INTO ft1(ft1, rowid, a) VALUES('delete', 2, 'two');
} {0 {}}
do_catchsql_test 4.2 {
  INSERT INTO ft2(ft2, rowid, a) VALUES('delete', 2, 'two');
} {1 {'delete' may not be used with a contentless_delete=1 table}}

do_execsql_test 4.3 {
  SELECT rowid, * FROM ft1;
} {
  1 {} i
  3 {} iii
}
do_execsql_test 4.4 {
  SELECT rowid, * FROM ft2;
} {
  1 {} i
  3 {} iii
}

do_execsql_test 4.5 {
  SELECT * FROM ft1_content
} {1 i 3 iii}

do_execsql_test 4.6 {
  SELECT * FROM ft2_content
} {1 i 3 iii}

finish_test

