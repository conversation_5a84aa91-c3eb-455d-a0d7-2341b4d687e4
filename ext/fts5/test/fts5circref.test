# 2018 Dec 22
#
# The author disclaims copyright to this source code.  In place of
# a legal notice, here is a blessing:
#
#    May you do good and not evil.
#    May you find forgiveness for yourself and forgive others.
#    May you share freely, never taking more than you give.
#
#*************************************************************************
# This file implements regression tests for SQLite library.  The
# focus of this script is testing the FTS5 module.
#

source [file join [file dirname [info script]] fts5_common.tcl]
set testprefix fts5circref

# If SQLITE_ENABLE_FTS5 is not defined, omit this file.
ifcapable !fts5 {
  finish_test
  return
}

do_execsql_test 1.0 {
  CREATE VIRTUAL TABLE tt USING fts5(a);
  SELECT name FROM sqlite_master ORDER BY 1;
} {
  tt tt_config tt_content tt_data tt_docsize tt_idx
}
db_save_and_close

foreach {tn schema sql} {
  1 {
    CREATE TRIGGER tr1 AFTER INSERT ON tt_config BEGIN
      SELECT * FROM tt;
    END;
  } {
    INSERT INTO tt(tt, rank) VALUES('usermerge', 4);
  }

  2 {
    CREATE TRIGGER tr1 AFTER INSERT ON tt_docsize BEGIN
      SELECT * FROM tt;
    END;
  } {
    INSERT INTO tt(a) VALUES('one two three');
  }

  3 {
    CREATE TRIGGER tr1 AFTER INSERT ON tt_content BEGIN
      SELECT * FROM tt;
    END;
  } {
    INSERT INTO tt(a) VALUES('one two three');
  }

  4 {
    CREATE TRIGGER tr1 AFTER INSERT ON tt_data BEGIN
      SELECT * FROM tt;
    END;
  } {
    INSERT INTO tt(a) VALUES('one two three');
  }

  5 {
    CREATE TRIGGER tr1 AFTER INSERT ON tt_idx BEGIN
      SELECT * FROM tt;
    END;
  } {
    INSERT INTO tt(a) VALUES('one two three');
  }
} {
  db_restore_and_reopen
  do_execsql_test 1.1.$tn.1 $schema
  do_catchsql_test 1.1.$tn.2 $sql {1 {database disk image is malformed}}
  db close
}


finish_test
