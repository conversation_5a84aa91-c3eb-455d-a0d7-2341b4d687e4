# 2014 June 17
#
# The author disclaims copyright to this source code.  In place of
# a legal notice, here is a blessing:
#
#    May you do good and not evil.
#    May you find forgiveness for yourself and forgive others.
#    May you share freely, never taking more than you give.
#
#*************************************************************************
#
# This file is focused on OOM errors.
#

source [file join [file dirname [info script]] fts5_common.tcl]
source $testdir/malloc_common.tcl
set testprefix fts5fault5

# If SQLITE_ENABLE_FTS3 is defined, omit this file.
ifcapable !fts5 {
  finish_test
  return
}

#-------------------------------------------------------------------------
# OOM while creating an FTS5 table.
#
do_faultsim_test 1.1 -faults oom-t* -prep {
  db eval { DROP TABLE IF EXISTS abc }
} -body {
  db eval { CREATE VIRTUAL TABLE abc USING fts5(x,y) }
} -test {
  faultsim_test_result {0 {}}
}


#-------------------------------------------------------------------------
# OOM while writing a multi-tier doclist-index. And while running
# integrity-check on the same.
#
reset_db
do_execsql_test 2.0 {
  CREATE VIRTUAL TABLE tt USING fts5(x);
  INSERT INTO tt(tt, rank) VALUES('pgsz', 32);
}
faultsim_save_and_close

do_faultsim_test 2.1 -faults oom-t* -prep {
  faultsim_restore_and_reopen
  db eval { SELECT * FROM tt }
} -body {
  set str [string repeat "abc " 50]
  db eval {
    WITH ii(i) AS (SELECT 1 UNION ALL SELECT i+1 FROM ii WHERE i<100)
      INSERT INTO tt(rowid, x) SELECT i, $str FROM ii;
  }
} -test {
  faultsim_test_result {0 {}}
}

do_faultsim_test 2.2 -faults oom-t* -body {
  db eval { INSERT INTO tt(tt) VALUES('integrity-check') }
} -test {
  faultsim_test_result {0 {}}
}

#-------------------------------------------------------------------------
# OOM while scanning fts5vocab tables.
#
reset_db
do_test 3.0 {
  execsql {
    CREATE VIRTUAL TABLE tt USING fts5(x);
    CREATE VIRTUAL TABLE tv USING fts5vocab(tt, 'row');

    CREATE VIRTUAL TABLE tt2 USING fts5(x, detail=col);
    CREATE VIRTUAL TABLE tv2 USING fts5vocab(tt2, 'col');

    INSERT INTO tt(tt, rank) VALUES('pgsz', 32);
    INSERT INTO tt2(tt2, rank) VALUES('pgsz', 32);
    BEGIN;
  }

  for {set i 0} {$i < 20} {incr i} {
    set str [string repeat "$i " 50]
    execsql { INSERT INTO tt VALUES($str) }
    execsql { INSERT INTO tt2 VALUES($str) }
  }
  execsql COMMIT
} {}

do_faultsim_test 3.1 -faults oom-t* -body {
  db eval {
    SELECT term FROM tv;
  }
} -test {
  faultsim_test_result {0 {0 1 10 11 12 13 14 15 16 17 18 19 2 3 4 5 6 7 8 9}}
}

do_faultsim_test 3.2 -faults oom-t* -body {
  db eval {
    SELECT term FROM tv WHERE term BETWEEN '1' AND '2';
  }
} -test {
  faultsim_test_result {0 {1 10 11 12 13 14 15 16 17 18 19 2}}
}

do_execsql_test 3.3.0 {
  SELECT * FROM tv2;
} {
  0 x 1 {} 1 x 1 {} 10 x 1 {} 11 x 1 {} 12 x 1 {} 13 x 1 {}        
  14 x 1 {} 15 x 1 {} 16 x 1 {} 17 x 1 {} 18 x 1 {} 19  x 1 {}     
  2 x 1 {} 3 x 1 {} 4 x 1 {} 5 x 1 {} 6 x 1 {} 7 x 1 {} 8 x 1 {}   
  9 x 1 {}
}
do_faultsim_test 3.3 -faults oom-t* -body {
  db eval {
    SELECT * FROM tv2;
  }
} -test {
  faultsim_test_result [list 0 [list                                   \
      0 x 1 {} 1 x 1 {} 10 x 1 {} 11 x 1 {} 12 x 1 {} 13 x 1 {}        \
      14 x 1 {} 15 x 1 {} 16 x 1 {} 17 x 1 {} 18 x 1 {} 19  x 1 {}     \
      2 x 1 {} 3 x 1 {} 4 x 1 {} 5 x 1 {} 6 x 1 {} 7 x 1 {} 8 x 1 {}   \
      9 x 1 {}
  ]]
}



finish_test
