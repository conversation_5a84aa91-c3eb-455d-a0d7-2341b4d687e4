# 2023 Feb 17
#
# The author disclaims copyright to this source code.  In place of
# a legal notice, here is a blessing:
#
#    May you do good and not evil.
#    May you find forgiveness for yourself and forgive others.
#    May you share freely, never taking more than you give.
#
#*************************************************************************
#

source [file join [file dirname [info script]] fts5_common.tcl]
ifcapable !fts5 { finish_test ; return }
set ::testprefix fts5secure6

db progress 1 progress_handler
set ::PHC 0
proc progress_handler {args} {
  incr ::PHC
  # if {($::PHC % 100000)==0}  breakpoint
  return 0
}

proc setup {} {
  db eval {
    DROP TABLE IF EXISTS t1;
    CREATE VIRTUAL TABLE t1 USING fts5(x);
    WITH s(i) AS (
      VALUES(1) UNION ALL SELECT i+1 FROM s WHERE i<1000
    )
    INSERT INTO t1 SELECT 'a b c d e f g h i j k' FROM s;
  }
}

foreach {tn sd} {
  1 0 
  2 1
} {
  setup
  do_execsql_test 1.$tn.0 { 
    INSERT INTO t1(t1, rank) VALUES('secure-delete', $sd) 
  }
  set PHC 0
  do_execsql_test 1.$tn.1 { DELETE FROM t1; }
  set phc($tn) $PHC
}

do_test 1.3 {
  expr $phc(1)*5 < $phc(2)
} {1}

#-------------------------------------------------------------------------
reset_db

do_execsql_test 2.0 {
  CREATE VIRTUAL TABLE t1 USING fts5(x);
  INSERT INTO t1(t1, rank) VALUES('secure-delete', $sd) 
}

do_execsql_test 2.1 {
  BEGIN;
    INSERT INTO t1(rowid, x) VALUES(-100000, 'abc def ghi');
    INSERT INTO t1(rowid, x) VALUES(-99999, 'abc def ghi');
    INSERT INTO t1(rowid, x) VALUES(9223372036854775800, 'abc def ghi');
  COMMIT;
}

do_execsql_test 2.2 {
  SELECT rowid FROM t1('def')
} {-100000 -99999 9223372036854775800}

#-------------------------------------------------------------------------
reset_db

do_execsql_test 3.0 {
  CREATE VIRTUAL TABLE t1 USING fts5(x);
  INSERT INTO t1(t1, rank) VALUES('secure-delete', $sd) 
}

do_execsql_test 3.1 {
  BEGIN;
    INSERT INTO t1(rowid, x) 
      VALUES(51869, 'when whenever where weress what turn'), 
            (51871, 'to were');
  COMMIT;
}

do_execsql_test 3.2 {
  DELETE FROM t1 WHERE rowid=51871;
  INSERT INTO t1(t1) VALUES('integrity-check');
}

#-------------------------------------------------------------------------
reset_db

do_execsql_test 4.0 {
  CREATE VIRTUAL TABLE t1 USING fts5(x);
  INSERT INTO t1(rowid, x) VALUES(10, 'one two');
}
do_execsql_test 4.1 {
  UPDATE t1 SET x = 'one three' WHERE rowid=10;
  INSERT INTO t1(t1, rank) VALUES('secure-delete', 1);
}
do_execsql_test 4.2 {
  DELETE FROM t1 WHERE rowid=10;
}
do_execsql_test 4.3 {
  INSERT INTO t1(t1) VALUES('integrity-check');
}

#-------------------------------------------------------------------------
reset_db

do_execsql_test 5.0 {
  CREATE VIRTUAL TABLE t1 USING fts5(content);

  INSERT INTO t1(t1,rank) VALUES('secure-delete',1);
  INSERT INTO t1 VALUES('active'),('boomer'),('atom'),('atomic'),
         ('alpha channel backup abandon test aback boomer atom alpha active');
  DELETE FROM t1 WHERE t1 MATCH 'abandon';
} 

do_execsql_test 5.1 {
  INSERT INTO t1(t1) VALUES('rebuild');
}

do_execsql_test 5.2 {
  DELETE FROM t1 WHERE rowid NOTNULL<5;
}

db close
sqlite3 db test.db

do_execsql_test 5.3 {
  PRAGMA integrity_check;
} {ok}


finish_test

