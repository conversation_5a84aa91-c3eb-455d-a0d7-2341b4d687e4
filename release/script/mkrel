#! /bin/sh
#
# mkrel
#
# Create a release for distribution.
# This is a wrapper for mkrelease that syncs to the
# Perforce head revision, reads the current
# version from $POCO_BASE/VERSION and requires a release
# specification (loaded from $POCO_BASE/release/spec/*.release)
# as argument.
#
# usage: mkrel [<specfile>]
#

if [ "$POCO_BASE" = "" ] ; then
  echo "Error: POCO_BASE not set."
  exit 1
fi

cd $POCO_BASE

if [ ! -f VERSION ] ; then
  echo "Error: No VERSION file found."
  exit 2
fi

case `uname` in
  CYGWIN*) cygwin=1
            ;;
        *) cygwin=""
            ;;
esac

label=""
spec=""
lineEndConv=""
while [ "$1" != "" ] ;
do
	if [ "$1" = "-l" ] ; then
		shift
		label=@$1
		shift
	elif [ "$1" = "-c" ] ; then
		shift
		lineEndConv=$1
		shift
	else
		spec=$1
		shift
	fi
done


if [ "$spec" != "" ] ; then
  relspec="-f release/spec/${spec}.release"
  reltag="-$spec"
else
  relspec=""
  reltag=""
fi

if [ "$lineEndConv" != "" ] ; then
  lnendcvt="-c ${lineEndConv}"
fi

if [ $cygwin ] ; then
  export PWD=`cygpath -w $POCO_BASE`
fi

#
# Sync files
#
if [ "$label" != "" ] ; then
	echo "Syncing files to ${label}..."
	p4 sync ./...$label
fi

read version <$POCO_BASE/VERSION
release=$version$reltag

#
# Build release
#
echo "Building release $release"

rm -rf releases/poco-$release.*
$POCO_BASE/release/script/mkrelease $release $relspec $lnendcvt

