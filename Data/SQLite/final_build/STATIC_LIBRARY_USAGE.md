# PocoDataSQLiteExternal 静态库使用说明

## 概述

本项目生成两种版本的库：
- **Debug版本**：动态库 (.dll + .lib导入库)
- **Release版本**：静态库 (.lib)

## 构建方法

### Debug版本（动态库）
```bash
cmake .. -G "Visual Studio 17 2022" -A x64 -DPOCO_BUILD_TYPE=SHARED
cmake --build . --config Debug
```

### Release版本（静态库）
```bash
cmake .. -G "Visual Studio 17 2022" -A x64 -DPOCO_BUILD_TYPE=STATIC
cmake --build . --config Release
```

## 在您的项目中使用静态库

### 方法1：使用CMake

```cmake
# 设置库路径
set(POCO_LIB_DIR "D:/CWorkspace/poco-poco-1.14.2-release/lib64")
set(SQLITE_LIB_DIR "D:/CWorkspace/poco-poco-1.14.2-release/Data/SQLite/final_build/lib/Release")

# 链接所有必需的库
target_link_libraries(your_target PRIVATE
    ${SQLITE_LIB_DIR}/PocoDataSQLiteExternal.lib
    ${POCO_LIB_DIR}/PocoFoundationmd.lib
    ${POCO_LIB_DIR}/PocoDatamd.lib
    ws2_32 iphlpapi crypt32
)

# 添加必要的编译定义
target_compile_definitions(your_target PRIVATE 
    POCO_STATIC
    SQLITE_THREADSAFE=1
    SQLITE_ENABLE_FTS5
    SQLITE_HAS_CODEC
)

# 添加包含目录
target_include_directories(your_target PRIVATE
    "D:/CWorkspace/poco-poco-1.14.2-release/Data/SQLite/include"
    "D:/CWorkspace/poco-poco-1.14.2-release/Foundation/include"
    "D:/CWorkspace/poco-poco-1.14.2-release/Data/include"
)
```

### 方法2：Visual Studio项目设置

**附加依赖项：**
- PocoDataSQLiteExternal.lib
- PocoFoundationmd.lib
- PocoDatamd.lib
- ws2_32.lib
- iphlpapi.lib
- crypt32.lib

**预处理器定义：**
- POCO_STATIC
- SQLITE_THREADSAFE=1
- SQLITE_ENABLE_FTS5
- SQLITE_HAS_CODEC

**附加包含目录：**
- D:/CWorkspace/poco-poco-1.14.2-release/Data/SQLite/include
- D:/CWorkspace/poco-poco-1.14.2-release/Foundation/include
- D:/CWorkspace/poco-poco-1.14.2-release/Data/include

## 重要说明

1. **库文件名**：确保使用正确的库文件名 `PocoFoundationmd.lib` 和 `PocoDatamd.lib`，而不是 `PocoFoundation.lib` 和 `PocoData.lib`

2. **运行时库**：确保您的项目使用 `/MD` (多线程DLL) 运行时库设置

3. **SQLCipher依赖**：静态库已经包含了SQLCipher的Release版本，无需额外链接

4. **编译定义**：`POCO_STATIC` 宏是必需的，用于正确处理Poco库的静态链接

## 故障排除

如果遇到链接错误 "cannot open file 'PocoFoundation.lib'"，请检查：
1. 是否使用了正确的库文件名 (PocoFoundationmd.lib)
2. 是否添加了 POCO_STATIC 宏定义
3. 是否使用了正确的运行时库设置 (/MD)
