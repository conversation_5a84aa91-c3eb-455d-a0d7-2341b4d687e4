
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{BA2B17D2-CD13-3F83-89CB-79B13F66E9C4}"
	ProjectSection(ProjectDependencies) = postProject
		{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0} = {05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}
		{28666966-23D0-30FA-9BDC-DC936E7297E5} = {28666966-23D0-30FA-9BDC-DC936E7297E5}
		{*************-3495-AB5F-4DC0FFC14FE5} = {*************-3495-AB5F-4DC0FFC14FE5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PocoDataSQLiteExternal", "PocoDataSQLiteExternal.vcxproj", "{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}"
	ProjectSection(ProjectDependencies) = postProject
		{28666966-23D0-30FA-9BDC-DC936E7297E5} = {28666966-23D0-30FA-9BDC-DC936E7297E5}
		{*************-3495-AB5F-4DC0FFC14FE5} = {*************-3495-AB5F-4DC0FFC14FE5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PocoDataSQLiteExternal_Objects", "PocoDataSQLiteExternal_Objects.vcxproj", "{28666966-23D0-30FA-9BDC-DC936E7297E5}"
	ProjectSection(ProjectDependencies) = postProject
		{*************-3495-AB5F-4DC0FFC14FE5} = {*************-3495-AB5F-4DC0FFC14FE5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{*************-3495-AB5F-4DC0FFC14FE5}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{BA2B17D2-CD13-3F83-89CB-79B13F66E9C4}.Debug|x64.ActiveCfg = Debug|x64
		{BA2B17D2-CD13-3F83-89CB-79B13F66E9C4}.Release|x64.ActiveCfg = Release|x64
		{BA2B17D2-CD13-3F83-89CB-79B13F66E9C4}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BA2B17D2-CD13-3F83-89CB-79B13F66E9C4}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}.Debug|x64.ActiveCfg = Debug|x64
		{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}.Debug|x64.Build.0 = Debug|x64
		{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}.Release|x64.ActiveCfg = Release|x64
		{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}.Release|x64.Build.0 = Release|x64
		{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{05DFC0FD-56E4-33CB-ADF1-05C27876FBD0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{28666966-23D0-30FA-9BDC-DC936E7297E5}.Debug|x64.ActiveCfg = Debug|x64
		{28666966-23D0-30FA-9BDC-DC936E7297E5}.Debug|x64.Build.0 = Debug|x64
		{28666966-23D0-30FA-9BDC-DC936E7297E5}.Release|x64.ActiveCfg = Release|x64
		{28666966-23D0-30FA-9BDC-DC936E7297E5}.Release|x64.Build.0 = Release|x64
		{28666966-23D0-30FA-9BDC-DC936E7297E5}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{28666966-23D0-30FA-9BDC-DC936E7297E5}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{28666966-23D0-30FA-9BDC-DC936E7297E5}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{28666966-23D0-30FA-9BDC-DC936E7297E5}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{*************-3495-AB5F-4DC0FFC14FE5}.Debug|x64.ActiveCfg = Debug|x64
		{*************-3495-AB5F-4DC0FFC14FE5}.Debug|x64.Build.0 = Debug|x64
		{*************-3495-AB5F-4DC0FFC14FE5}.Release|x64.ActiveCfg = Release|x64
		{*************-3495-AB5F-4DC0FFC14FE5}.Release|x64.Build.0 = Release|x64
		{*************-3495-AB5F-4DC0FFC14FE5}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{*************-3495-AB5F-4DC0FFC14FE5}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{*************-3495-AB5F-4DC0FFC14FE5}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{*************-3495-AB5F-4DC0FFC14FE5}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5AD00559-81B4-37B3-BE01-176AA0C3941F}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
