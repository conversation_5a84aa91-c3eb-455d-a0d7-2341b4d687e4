cmake_minimum_required(VERSION 3.15.0)

project(PocoDataSQLiteExternal VERSION 1.14.2)

# 设置C++17标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# SQLCipher路径
set(SQLCIPHER_INCLUDE_DIR "D:/BWorkspace/TestFindGame/Thirdparty/Sqlcipher/Include")
set(SQLCIPHER_LIB_DIR "D:/BWorkspace/TestFindGame/Thirdparty/Sqlcipher/Debug/lib")

# 查找SQLCipher库
find_library(SQLCIPHER_LIBRARY
    NAMES sqlite3 sqlcipher
    PATHS ${SQLCIPHER_LIB_DIR}
    NO_DEFAULT_PATH
)

if(NOT SQLCIPHER_LIBRARY)
    message(FATAL_ERROR "SQLCipher library not found")
endif()

message(STATUS "SQLCipher library: ${SQLCIPHER_LIBRARY}")

# 源文件列表
set(SOURCES
    src/Binder.cpp
    src/Connector.cpp
    src/Extractor.cpp
    src/Notifier.cpp
    src/SessionImpl.cpp
    src/SQLiteException.cpp
    src/SQLiteStatementImpl.cpp
    src/Utility.cpp
)

# 创建动态库
add_library(PocoDataSQLiteExternal SHARED ${SOURCES})

# 设置目标属性
set_target_properties(PocoDataSQLiteExternal PROPERTIES
    OUTPUT_NAME "PocoDataSQLiteExternal"
    VERSION ${PROJECT_VERSION}
)

# 包含目录
target_include_directories(PocoDataSQLiteExternal PRIVATE
    include
    ../../Foundation/include
    ../../Data/include
    ${SQLCIPHER_INCLUDE_DIR}
)

# 编译定义
target_compile_definitions(PocoDataSQLiteExternal PRIVATE
    WIN32
    _WINDOWS
    _USRDLL
    SQLite_EXPORTS
    POCO_UNBUNDLED
    SQLITE_THREADSAFE=1
    SQLITE_ENABLE_FTS5
    SQLITE_HAS_CODEC
    $<$<CONFIG:Debug>:_DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
)

# Windows特定设置
if(WIN32)
    target_compile_definitions(PocoDataSQLiteExternal PRIVATE
        _CRT_SECURE_NO_WARNINGS
        _SCL_SECURE_NO_WARNINGS
    )

    target_compile_options(PocoDataSQLiteExternal PRIVATE
        /wd4996 /wd4244 /wd4018
    )
endif()

# Poco库路径
set(POCO_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../../lib64")

# 查找Poco库
find_library(POCO_FOUNDATION_LIBRARY
    NAMES PocoFoundationmdd PocoFoundationmd PocoFoundationd PocoFoundation
    PATHS ${POCO_LIB_DIR}
    NO_DEFAULT_PATH
)

find_library(POCO_DATA_LIBRARY
    NAMES PocoDatamdd PocoDatamd PocoDatad PocoData
    PATHS ${POCO_LIB_DIR}
    NO_DEFAULT_PATH
)

if(NOT POCO_FOUNDATION_LIBRARY)
    message(FATAL_ERROR "Poco Foundation library not found in ${POCO_LIB_DIR}")
endif()

if(NOT POCO_DATA_LIBRARY)
    message(FATAL_ERROR "Poco Data library not found in ${POCO_LIB_DIR}")
endif()

message(STATUS "Poco Foundation library: ${POCO_FOUNDATION_LIBRARY}")
message(STATUS "Poco Data library: ${POCO_DATA_LIBRARY}")

# 链接库
target_link_libraries(PocoDataSQLiteExternal PRIVATE
    ${POCO_FOUNDATION_LIBRARY}
    ${POCO_DATA_LIBRARY}
    ${SQLCIPHER_LIBRARY}
)

# Windows系统库
if(WIN32)
    target_link_libraries(PocoDataSQLiteExternal PRIVATE
        ws2_32 iphlpapi crypt32
    )
endif()

message(STATUS "Configuration complete")
message(STATUS "SQLCipher: ${SQLCIPHER_LIBRARY}")
message(STATUS "Output: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")