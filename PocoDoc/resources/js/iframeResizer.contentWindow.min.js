/*! iFrame Resizer (iframeSizer.contentWindow.min.js) - v2.6.2 - 2014-10-11
 *  Desc: Include this file in any page being loaded into an iframe
 *        to force the iframe to resize to the content size.
 *  Requires: iframeResizer.min.js on host page.
 *  Copyright: (c) 2014 <PERSON> - <PERSON>@bradshaw.net
 *  License: MIT
 */

!function(){"use strict";function a(a,b,c){"addEventListener"in window?a.addEventListener(b,c,!1):"attachEvent"in window&&a.attachEvent("on"+b,c)}function b(a){return $+"["+ab+"] "+a}function c(a){Z&&"object"==typeof window.console&&console.log(b(a))}function d(a){"object"==typeof window.console&&console.warn(b(a))}function e(){c("Initialising iFrame"),f(),i(),h("background",L),h("padding",O),o(),m(),j(),p(),n(),D("init","Init message from host page")}function f(){function a(a){return"true"===a?!0:!1}var b=X.substr(_).split(":");ab=b[0],M=void 0!==b[1]?Number(b[1]):M,P=void 0!==b[2]?a(b[2]):P,Z=void 0!==b[3]?a(b[3]):Z,Y=void 0!==b[4]?Number(b[4]):Y,bb=void 0!==b[5]?a(b[5]):bb,J=void 0!==b[6]?a(b[6]):J,N=b[7],V=void 0!==b[8]?b[8]:V,L=b[9],O=b[10],fb=void 0!==b[11]?Number(b[11]):fb}function g(a,b){return-1!==b.indexOf("-")&&(d("Negative CSS value ignored for "+a),b=""),b}function h(a,b){void 0!==b&&""!==b&&"null"!==b&&(document.body.style[a]=b,c("Body "+a+' set to "'+b+'"'))}function i(){void 0===N&&(N=M+"px"),g("margin",N),h("margin",N)}function j(){document.documentElement.style.height="",document.body.style.height="",c('HTML & body height set to "auto"')}function k(){a(window,"resize",function(){D("resize","Window resized")})}function l(){a(window,"click",function(){D("click","Window clicked")})}function m(){U!==V&&(V in jb||(d(V+" is not a valid option for heightCalculationMethod."),V="bodyScroll"),c('Height calculation method set to "'+V+'"'))}function n(){!0===J?(k(),l(),s()):c("Auto Resize disabled")}function o(){var a=document.createElement("div");a.style.clear="both",a.style.display="block",document.body.appendChild(a)}function p(){bb&&(c("Enable public methods"),window.parentIFrame={close:function(){D("close","parentIFrame.close()",0,0)},getId:function(){return ab},reset:function(){G("parentIFrame.size")},scrollTo:function(a,b){H(b,a,"scrollTo")},sendMessage:function(a,b){H(0,0,"message",a,b)},setHeightCalculationMethod:function(a){V=a,m()},setTargetOrigin:function(a){c("Set targetOrigin: "+a),db=a},size:function(a,b){var c=""+(a?a:"")+(b?","+b:"");E(),D("size","parentIFrame.size("+c+")",a,b)}})}function q(){0!==Y&&(c("setInterval: "+Y+"ms"),setInterval(function(){D("interval","setInterval: "+Y)},Math.abs(Y)))}function r(b){function d(b){(void 0===b.height||void 0===b.width||0===b.height||0===b.width)&&(c("Attach listerner to "+b.src),a(b,"load",function(){D("imageLoad","Image loaded")}))}b.forEach(function(a){if("attributes"===a.type&&"src"===a.attributeName)d(a.target);else if("childList"===a.type){var b=a.target.querySelectorAll("img");Array.prototype.forEach.call(b,function(a){d(a)})}})}function s(){function a(){var a=document.querySelector("body"),d={attributes:!0,attributeOldValue:!1,characterData:!0,characterDataOldValue:!1,childList:!0,subtree:!0},e=new b(function(a){D("mutationObserver","mutationObserver: "+a[0].target+" "+a[0].type),r(a)});c("Enable MutationObserver"),e.observe(a,d)}var b=window.MutationObserver||window.WebKitMutationObserver;b?0>Y?q():a():(d("MutationObserver not supported in this browser!"),q())}function t(){function a(a){function b(a){var b=/^\d+(px)?$/i;if(b.test(a))return parseInt(a,K);var d=c.style.left,e=c.runtimeStyle.left;return c.runtimeStyle.left=c.currentStyle.left,c.style.left=a||0,a=c.style.pixelLeft,c.style.left=d,c.runtimeStyle.left=e,a}var c=document.body,d=0;return"defaultView"in document&&"getComputedStyle"in document.defaultView?(d=document.defaultView.getComputedStyle(c,null),d=null!==d?d[a]:0):d=b(c.currentStyle[a]),parseInt(d,K)}return document.body.offsetHeight+a("marginTop")+a("marginBottom")}function u(){return document.body.scrollHeight}function v(){return document.documentElement.offsetHeight}function w(){return document.documentElement.scrollHeight}function x(){for(var a=document.querySelectorAll("body *"),b=a.length,d=0,e=(new Date).getTime(),f=0;b>f;f++)a[f].getBoundingClientRect().bottom>d&&(d=a[f].getBoundingClientRect().bottom);return e=(new Date).getTime()-e,c("Parsed "+b+" HTML elements"),c("LowestElement bottom position calculated in "+e+"ms"),d}function y(){return[t(),u(),v(),w()]}function z(){return Math.max.apply(null,y())}function A(){return Math.min.apply(null,y())}function B(){return Math.max(t(),x())}function C(){return Math.max(document.documentElement.scrollWidth,document.body.scrollWidth)}function D(a,b,d,e){function f(){a in{reset:1,resetPage:1,init:1}||c("Trigger event: "+b)}function g(){S=n,ib=o,H(S,ib,a)}function h(){return gb&&a in Q}function i(){function a(a,b){var c=Math.abs(a-b)<=fb;return!c}return n=void 0!==d?d:jb[V](),o=void 0!==e?e:C(),a(S,n)||P&&a(ib,o)}function j(){return!(a in{init:1,interval:1,size:1})}function k(){return V in cb}function l(){c("No change in size detected")}function m(){j()&&k()?G(b):a in{interval:1}||(f(),l())}var n,o;h()?c("Trigger event cancelled: "+a):i()?(f(),E(),g()):m()}function E(){gb||(gb=!0,c("Trigger event lock on")),clearTimeout(hb),hb=setTimeout(function(){gb=!1,c("Trigger event lock off"),c("--")},R)}function F(a){S=jb[V](),ib=C(),H(S,ib,a)}function G(a){var b=V;V=U,c("Reset trigger event: "+a),E(),F("reset"),V=b}function H(a,b,d,e,f){function g(){void 0===f?f=db:c("Message targetOrigin: "+f)}function h(){var g=a+":"+b,h=ab+":"+g+":"+d+(void 0!==e?":"+e:"");c("Sending message to host page ("+h+")"),eb.postMessage($+h,f)}g(),h()}function I(a){function b(){return $===(""+a.data).substr(0,_)}function f(){X=a.data,eb=a.source,e(),T=!1,setTimeout(function(){W=!1},R)}function g(){W?c("Page reset ignored by init"):(c("Page size reset by host page"),F("resetPage"))}function h(){return a.data.split("]")[1]}function i(){return"iFrameResize"in window}function j(){return a.data.split(":")[2]in{"true":1,"false":1}}b()&&(T&&j()?f():"reset"===h()?g():a.data===X||i()||d("Unexpected message ("+a.data+")"))}var J=!0,K=10,L="",M=0,N="",O="",P=!1,Q={resize:1,click:1},R=128,S=1,T=!0,U="offset",V=U,W=!0,X="",Y=32,Z=!1,$="[iFrameSizer]",_=$.length,ab="",bb=!1,cb={max:1,scroll:1,bodyScroll:1,documentElementScroll:1},db="*",eb=window.parent,fb=0,gb=!1,hb=null,ib=1,jb={offset:t,bodyOffset:t,bodyScroll:u,documentElementOffset:v,scroll:w,documentElementScroll:w,max:z,min:A,grow:z,lowestElement:B};a(window,"message",I)}();
//# sourceMappingURL=../src/iframeResizer.contentWindow.map