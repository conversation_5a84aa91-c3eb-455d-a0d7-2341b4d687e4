#include <QtTest>
#include <QDebug>
#include <QDateTime>
#include <QDir>
#include <QStandardPaths>
#include <QFile>
#include <QEventLoop>
#include <QTimer>
#include <QAtomicInt>
#include <QSet>
#include <memory>
#include <climits>

// 包含需要测试的类
#include "Test.h"
#include "DatabaseManager.h"
#include "TestRepository.h"

/**
 * @brief TestTest类 - 充分利用POCO ORM特性的测试类
 *
 * 测试Test实体类和TestRepository的ORM功能，包括：
 * - 数据库连接和初始化
 * - 实体类的基础功能
 * - CRUD操作
 * - 查询功能
 * - ORM特性（时间戳、数据转换等）
 * - 异常处理
 * - 性能测试
 */
class TestTest : public QObject {
    Q_OBJECT

public:
    TestTest();

    ~TestTest();

private slots:
    // 测试初始化和清理
    void initTestCase();

    void cleanupTestCase();

    void init();

    void cleanup();

    // ==================== TestRepository基础功能测试 ====================

    /**
     * @brief 测试Repository的基础状态检查
     */
    void testRepositoryBasics();

    // ==================== CRUD操作测试 ====================

    /**
     * @brief 测试创建Test记录
     */
    void testCreateTest();

    /**
     * @brief 测试根据ID获取Test记录
     */
    void testGetTestById();

    /**
     * @brief 测试获取所有Test记录
     */
    void testGetAllTests();

    /**
     * @brief 测试根据名称查询Test记录
     */
    void testGetTestsByName();

    /**
     * @brief 测试更新Test记录
     */
    void testUpdateTest();

    /**
     * @brief 测试删除Test记录
     */
    void testDeleteTest();

    /**
     * @brief 测试删除所有Test记录
     */
    void testDeleteAllTests();

    /**
     * @brief 测试获取记录总数
     */
    void testGetTestCount();

    // ==================== 高级ORM特性测试 ====================

    /**
     * @brief 测试批量创建Test记录
     */
    void testBatchCreateTests();

    /**
     * @brief 测试分页查询Test记录
     */
    void testGetTestsPaginated();

    /**
     * @brief 测试批量更新Test记录
     */
    void testBatchUpdateTests();

    // ==================== 错误处理和边界条件测试 ====================

    /**
     * @brief 测试错误处理和异常情况
     */
    void testErrorHandling();

    /**
     * @brief 测试边界条件
     */
    void testBoundaryConditions();

    // ==================== 性能和并发测试 ====================

    /**
     * @brief 测试异步操作的并发性
     */
    void testConcurrentOperations();

private:
    DatabaseManager *m_dbManager;
    TestRepository *m_testRepository;
    QString m_testDbPath;

    // 辅助方法
    void setupTestDatabase();

    void cleanupTestDatabase();

    /**
     * @brief 等待异步操作完成的辅助方法
     * @param timeout 超时时间（毫秒）
     * @return 是否在超时前完成
     */
    bool waitForAsyncOperation(int timeout = 5000);

    /**
     * @brief 创建测试数据的辅助方法
     * @param count 创建的记录数量
     * @return 创建的Test对象列表
     */
    QList<Test> createTestData(int count = 3);

    /**
     * @brief 验证Test对象是否相等的辅助方法
     * @param expected 期望的Test对象
     * @param actual 实际的Test对象
     * @param checkId 是否检查ID（对于新创建的对象可能不需要）
     */
    void verifyTestObject(const Test& expected, const Test& actual, bool checkId = true);

    /**
     * @brief 清空数据库中的所有测试数据
     */
    void clearAllTestData();
};

TestTest::TestTest()
    : m_dbManager(nullptr)
      , m_testRepository(nullptr) {
}

TestTest::~TestTest() {
}

void TestTest::initTestCase() {
    qDebug() << "=== 开始POCO ORM测试 ===";

    // 设置测试数据库路径
    QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    m_testDbPath = tempDir + "/test_orm.db";

    qDebug() << "测试数据库路径:" << m_testDbPath;

    // 确保测试开始前删除旧的测试数据库
    if (QFile::exists(m_testDbPath)) {
        QFile::remove(m_testDbPath);
    }
}

void TestTest::cleanupTestCase() {
    qDebug() << "=== 清理POCO ORM测试 ===";

    // 清理资源
    delete m_testRepository;
    delete m_dbManager;

    // 删除测试数据库文件
    if (QFile::exists(m_testDbPath)) {
        QFile::remove(m_testDbPath);
        qDebug() << "已删除测试数据库文件";
    }
}

void TestTest::init() {
    // 每个测试方法执行前的初始化
    setupTestDatabase();
}

void TestTest::cleanup() {
    // 每个测试方法执行后的清理
    cleanupTestDatabase();
}

void TestTest::setupTestDatabase() {
    // 创建DatabaseManager实例
    m_dbManager = new DatabaseManager();

    // 初始化数据库
    if (!m_dbManager->initialize(m_testDbPath)) {
        qCritical() << "Failed to initialize test database:" << m_testDbPath;
        return;
    }

    // 创建数据表
    if (!m_dbManager->createTables()) {
        qCritical() << "Failed to create tables in test database";
        return;
    }

    // 创建TestRepository实例
    m_testRepository = new TestRepository(m_dbManager);

    // 启动工作线程 - 这是关键的修复
    m_testRepository->start();

    qDebug() << "Test database setup completed successfully";
}

void TestTest::cleanupTestDatabase() {
    // 清理TestRepository
    if (m_testRepository) {
        // 停止工作线程
        m_testRepository->stop();
        delete m_testRepository;
        m_testRepository = nullptr;
    }

    // 清理DatabaseManager
    if (m_dbManager) {
        delete m_dbManager;
        m_dbManager = nullptr;
    }

    qDebug() << "Test database cleanup completed";
}

// ==================== 辅助方法实现 ====================

bool TestTest::waitForAsyncOperation(int timeout) {
    QEventLoop loop;
    QTimer timer;
    timer.setSingleShot(true);
    timer.setInterval(timeout);

    bool completed = false;

    // 连接定时器超时信号
    connect(&timer, &QTimer::timeout, [&loop, &completed]() {
        completed = false;
        loop.quit();
    });

    // 启动定时器
    timer.start();

    // 等待操作完成或超时
    loop.exec();

    return completed;
}

QList<Test> TestTest::createTestData(int count) {
    QList<Test> testData;

    for (int i = 1; i <= count; ++i) {
        Test test(
            QString("测试记录%1").arg(i).toStdString(),
            QString("这是第%1个测试记录的描述").arg(i).toStdString(),
            i * 10
        );
        testData.append(test);
    }

    return testData;
}

void TestTest::verifyTestObject(const Test& expected, const Test& actual, bool checkId) {
    if (checkId) {
        QCOMPARE(actual.getId(), expected.getId());
    }
    QCOMPARE(QString::fromStdString(actual.getName()), QString::fromStdString(expected.getName()));
    QCOMPARE(QString::fromStdString(actual.getDescription()), QString::fromStdString(expected.getDescription()));
    QCOMPARE(actual.getValue(), expected.getValue());
    // 注意：时间戳可能会有微小差异，这里不做严格比较
}

void TestTest::clearAllTestData() {
    if (!m_testRepository) {
        return;
    }

    QEventLoop loop;
    bool operationCompleted = false;

    m_testRepository->deleteAllTests([&loop, &operationCompleted](const AsyncResult<bool>& result) {
        operationCompleted = true;
        loop.quit();
    });

    // 等待操作完成
    QTimer::singleShot(5000, &loop, &QEventLoop::quit); // 5秒超时
    loop.exec();

    if (!operationCompleted) {
        qWarning() << "清空测试数据超时";
    }
}

// ==================== TestRepository基础功能测试 ====================

void TestTest::testRepositoryBasics() {
    qDebug() << "\n=== 测试Repository基础功能 ===";

    // 测试Repository是否可用
    QVERIFY(m_testRepository != nullptr);
    QVERIFY(m_testRepository->isValid());

    // 测试DatabaseManager是否可用
    QVERIFY(m_dbManager != nullptr);
    QVERIFY(m_dbManager->isConnected());

    qDebug() << "✓ Repository基础功能测试通过";
}

// ==================== CRUD操作测试 ====================

void TestTest::testCreateTest() {
    qDebug() << "\n=== 测试创建Test记录 ===";

    // 准备测试数据
    Test testData("创建测试", "测试创建功能", 100);

    QEventLoop loop;
    bool operationCompleted = false;
    int createdId = -1;
    bool success = false;
    QString errorMessage;

    // 执行异步创建操作
    qDebug() << "开始执行异步创建操作...";
    m_testRepository->createTest(testData, [&](const AsyncResult<int>& result) {
        qDebug() << "回调函数被调用！success:" << result.success;
        operationCompleted = true;
        success = result.success;
        if (success) {
            createdId = result.result;
            qDebug() << "✓ 创建Test记录测试通过，创建的ID:" << createdId;
        } else {
            errorMessage = result.errorMessage;
            qDebug() << "创建失败:" << errorMessage;
        }
        loop.quit();
    });

    qDebug() << "异步操作已提交，等待回调...";

    // 等待操作完成
    QTimer timeoutTimer;
    timeoutTimer.setSingleShot(true);
    timeoutTimer.setInterval(5000); // 5秒超时

    connect(&timeoutTimer, &QTimer::timeout, [&]() {
        qWarning() << "测试超时！回调未在5秒内执行";
        loop.quit();
    });

    timeoutTimer.start();
    loop.exec();
    timeoutTimer.stop();

    // 验证结果
    QVERIFY2(operationCompleted, "创建操作未在超时时间内完成");
    QVERIFY2(success, QString("创建操作失败: %1").arg(errorMessage).toLocal8Bit().constData());
    QVERIFY(createdId > 0);

    qDebug() << "✓ 创建Test记录测试通过，创建的ID:" << createdId;
}

void TestTest::testGetTestById() {
    qDebug() << "\n=== 测试根据ID获取Test记录 ===";

    // 首先创建一个测试记录
    Test testData("查询测试", "测试根据ID查询功能", 200);

    QEventLoop loop;
    int createdId = -1;

    // 创建记录
    m_testRepository->createTest(testData, [&](const AsyncResult<int>& result) {
        if (result.success) {
            createdId = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(createdId > 0);

    // 现在测试查询功能
    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;
    std::unique_ptr<Test> retrievedTest;

    m_testRepository->getTestById(createdId, [&](const AsyncResult<std::unique_ptr<Test>>& result) {
        operationCompleted = true;
        success = result.success;
        if (success && result.result) {
            retrievedTest = std::make_unique<Test>(*result.result);
        } else {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证结果
    QVERIFY2(operationCompleted, "查询操作未在超时时间内完成");
    QVERIFY2(success, QString("查询操作失败: %1").arg(errorMessage).toLocal8Bit().constData());
    QVERIFY(retrievedTest != nullptr);

    // 验证数据正确性
    QCOMPARE(retrievedTest->getId(), createdId);
    QCOMPARE(QString::fromStdString(retrievedTest->getName()), QString::fromStdString(testData.getName()));
    QCOMPARE(QString::fromStdString(retrievedTest->getDescription()), QString::fromStdString(testData.getDescription()));
    QCOMPARE(retrievedTest->getValue(), testData.getValue());

    qDebug() << "✓ 根据ID获取Test记录测试通过";
}

void TestTest::testGetAllTests() {
    qDebug() << "\n=== 测试获取所有Test记录 ===";

    // 清空现有数据
    clearAllTestData();

    // 创建多个测试记录
    QList<Test> testDataList = createTestData(3);

    QEventLoop loop;

    // 逐个创建记录
    for (const Test& testData : testDataList) {
        bool createCompleted = false;
        m_testRepository->createTest(testData, [&](const AsyncResult<int>& result) {
            createCompleted = true;
            loop.quit();
        });

        QTimer::singleShot(5000, &loop, &QEventLoop::quit);
        loop.exec();
        QVERIFY(createCompleted);
    }

    // 现在测试获取所有记录
    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;
    QList<Test> retrievedTests;

    m_testRepository->getAllTests([&](const AsyncResult<QList<Test>>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            retrievedTests = result.result;
        } else {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证结果
    QVERIFY2(operationCompleted, "获取所有记录操作未在超时时间内完成");
    QVERIFY2(success, QString("获取所有记录操作失败: %1").arg(errorMessage).toLocal8Bit().constData());
    QCOMPARE(retrievedTests.size(), testDataList.size());

    qDebug() << "✓ 获取所有Test记录测试通过，共获取到" << retrievedTests.size() << "条记录";
}

void TestTest::testGetTestsByName() {
    qDebug() << "\n=== 测试根据名称查询Test记录 ===";

    // 清空现有数据
    clearAllTestData();

    // 创建测试数据，包含重复名称
    QList<Test> testDataList;
    testDataList.append(Test("重复名称", "第一个重复名称记录", 100));
    testDataList.append(Test("重复名称", "第二个重复名称记录", 200));
    testDataList.append(Test("唯一名称", "唯一名称记录", 300));

    QEventLoop loop;

    // 创建所有记录
    for (const Test& testData : testDataList) {
        bool createCompleted = false;
        m_testRepository->createTest(testData, [&](const AsyncResult<int>& result) {
            createCompleted = true;
            loop.quit();
        });

        QTimer::singleShot(5000, &loop, &QEventLoop::quit);
        loop.exec();
        QVERIFY(createCompleted);
    }

    // 测试查询重复名称
    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;
    QList<Test> retrievedTests;

    m_testRepository->getTestsByName("重复名称", [&](const AsyncResult<QList<Test>>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            retrievedTests = result.result;
        } else {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证结果
    QVERIFY2(operationCompleted, "根据名称查询操作未在超时时间内完成");
    QVERIFY2(success, QString("根据名称查询操作失败: %1").arg(errorMessage).toLocal8Bit().constData());
    QCOMPARE(retrievedTests.size(), 2); // 应该找到2条重复名称的记录

    // 验证所有返回的记录都有正确的名称
    for (const Test& test : retrievedTests) {
        QCOMPARE(QString::fromStdString(test.getName()), QString("重复名称"));
    }

    qDebug() << "✓ 根据名称查询Test记录测试通过，找到" << retrievedTests.size() << "条匹配记录";
}

void TestTest::testUpdateTest() {
    qDebug() << "\n=== 测试更新Test记录 ===";

    // 首先创建一个测试记录
    Test originalData("原始名称", "原始描述", 100);

    QEventLoop loop;
    int createdId = -1;

    // 创建记录
    m_testRepository->createTest(originalData, [&](const AsyncResult<int>& result) {
        if (result.success) {
            createdId = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(createdId > 0);

    // 现在测试更新功能
    QString newName = "更新后的名称";
    QString newDescription = "更新后的描述";
    int newValue = 999;

    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;

    m_testRepository->updateTest(createdId, newName, newDescription, newValue,
                                [&](const AsyncResult<bool>& result) {
        operationCompleted = true;
        success = result.success;
        if (!success) {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证更新操作结果
    QVERIFY2(operationCompleted, "更新操作未在超时时间内完成");
    QVERIFY2(success, QString("更新操作失败: %1").arg(errorMessage).toLocal8Bit().constData());

    // 查询更新后的记录验证
    bool queryCompleted = false;
    std::unique_ptr<Test> updatedTest;

    m_testRepository->getTestById(createdId, [&](const AsyncResult<std::unique_ptr<Test>>& result) {
        queryCompleted = true;
        if (result.success && result.result) {
            updatedTest = std::make_unique<Test>(*result.result);
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证更新后的数据
    QVERIFY(queryCompleted);
    QVERIFY(updatedTest != nullptr);
    QCOMPARE(updatedTest->getId(), createdId);
    QCOMPARE(QString::fromStdString(updatedTest->getName()), newName);
    QCOMPARE(QString::fromStdString(updatedTest->getDescription()), newDescription);
    QCOMPARE(updatedTest->getValue(), newValue);

    qDebug() << "✓ 更新Test记录测试通过";
}

void TestTest::testDeleteTest() {
    qDebug() << "\n=== 测试删除Test记录 ===";

    // 首先创建一个测试记录
    Test testData("待删除记录", "这个记录将被删除", 500);

    QEventLoop loop;
    int createdId = -1;

    // 创建记录
    m_testRepository->createTest(testData, [&](const AsyncResult<int>& result) {
        if (result.success) {
            createdId = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(createdId > 0);

    // 现在测试删除功能
    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;

    m_testRepository->deleteTest(createdId, [&](const AsyncResult<bool>& result) {
        operationCompleted = true;
        success = result.success;
        if (!success) {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证删除操作结果
    QVERIFY2(operationCompleted, "删除操作未在超时时间内完成");
    QVERIFY2(success, QString("删除操作失败: %1").arg(errorMessage).toLocal8Bit().constData());

    // 尝试查询已删除的记录，应该查询不到
    bool queryCompleted = false;
    std::unique_ptr<Test> deletedTest;

    m_testRepository->getTestById(createdId, [&](const AsyncResult<std::unique_ptr<Test>>& result) {
        queryCompleted = true;
        if (result.success && result.result) {
            deletedTest = std::make_unique<Test>(*result.result);
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证记录已被删除（查询不到）
    QVERIFY(queryCompleted);
    QVERIFY(deletedTest == nullptr);

    qDebug() << "✓ 删除Test记录测试通过";
}

void TestTest::testDeleteAllTests() {
    qDebug() << "\n=== 测试删除所有Test记录 ===";

    // 清空现有数据并创建多个测试记录
    clearAllTestData();
    QList<Test> testDataList = createTestData(5);

    QEventLoop loop;

    // 创建多个记录
    for (const Test& testData : testDataList) {
        bool createCompleted = false;
        m_testRepository->createTest(testData, [&](const AsyncResult<int>& result) {
            createCompleted = true;
            loop.quit();
        });

        QTimer::singleShot(5000, &loop, &QEventLoop::quit);
        loop.exec();
        QVERIFY(createCompleted);
    }

    // 验证记录已创建
    bool countCompleted = false;
    int recordCount = 0;

    m_testRepository->getTestCount([&](const AsyncResult<int>& result) {
        countCompleted = true;
        if (result.success) {
            recordCount = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(countCompleted);
    QCOMPARE(recordCount, testDataList.size());

    // 现在测试删除所有记录
    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;

    m_testRepository->deleteAllTests([&](const AsyncResult<bool>& result) {
        operationCompleted = true;
        success = result.success;
        if (!success) {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证删除操作结果
    QVERIFY2(operationCompleted, "删除所有记录操作未在超时时间内完成");
    QVERIFY2(success, QString("删除所有记录操作失败: %1").arg(errorMessage).toLocal8Bit().constData());

    // 验证所有记录已被删除
    countCompleted = false;
    recordCount = -1;

    m_testRepository->getTestCount([&](const AsyncResult<int>& result) {
        countCompleted = true;
        if (result.success) {
            recordCount = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(countCompleted);
    QCOMPARE(recordCount, 0);

    qDebug() << "✓ 删除所有Test记录测试通过";
}

void TestTest::testGetTestCount() {
    qDebug() << "\n=== 测试获取记录总数 ===";

    // 清空现有数据
    clearAllTestData();

    QEventLoop loop;

    // 测试空数据库的记录数
    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;
    int count = -1;

    m_testRepository->getTestCount([&](const AsyncResult<int>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            count = result.result;
        } else {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证空数据库的记录数
    QVERIFY2(operationCompleted, "获取记录总数操作未在超时时间内完成");
    QVERIFY2(success, QString("获取记录总数操作失败: %1").arg(errorMessage).toLocal8Bit().constData());
    QCOMPARE(count, 0);

    // 创建一些记录
    QList<Test> testDataList = createTestData(3);

    for (const Test& testData : testDataList) {
        bool createCompleted = false;
        m_testRepository->createTest(testData, [&](const AsyncResult<int>& result) {
            createCompleted = true;
            loop.quit();
        });

        QTimer::singleShot(5000, &loop, &QEventLoop::quit);
        loop.exec();
        QVERIFY(createCompleted);
    }

    // 再次测试记录数
    operationCompleted = false;
    count = -1;

    m_testRepository->getTestCount([&](const AsyncResult<int>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            count = result.result;
        } else {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证记录数正确
    QVERIFY(operationCompleted);
    QVERIFY(success);
    QCOMPARE(count, testDataList.size());

    qDebug() << "✓ 获取记录总数测试通过，当前记录数:" << count;
}

// ==================== 高级ORM特性测试 ====================

void TestTest::testBatchCreateTests() {
    qDebug() << "\n=== 测试批量创建Test记录 ===";

    // 清空现有数据
    clearAllTestData();

    // 准备批量测试数据
    QList<Test> batchTestData;
    for (int i = 1; i <= 10; ++i) {
        Test test(
            QString("批量测试%1").arg(i).toStdString(),
            QString("批量创建的第%1个记录").arg(i).toStdString(),
            i * 50
        );
        batchTestData.append(test);
    }

    QEventLoop loop;
    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;
    int createdCount = 0;

    // 执行批量创建操作
    m_testRepository->batchCreateTests(batchTestData, [&](const AsyncResult<int>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            createdCount = result.result;
        } else {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(10000, &loop, &QEventLoop::quit); // 批量操作可能需要更长时间
    loop.exec();

    // 验证批量创建结果
    QVERIFY2(operationCompleted, "批量创建操作未在超时时间内完成");
    QVERIFY2(success, QString("批量创建操作失败: %1").arg(errorMessage).toLocal8Bit().constData());
    QCOMPARE(createdCount, batchTestData.size());

    // 验证记录确实被创建
    bool countCompleted = false;
    int totalCount = 0;

    m_testRepository->getTestCount([&](const AsyncResult<int>& result) {
        countCompleted = true;
        if (result.success) {
            totalCount = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(countCompleted);
    QCOMPARE(totalCount, batchTestData.size());

    qDebug() << "✓ 批量创建Test记录测试通过，成功创建" << createdCount << "条记录";
}

void TestTest::testGetTestsPaginated() {
    qDebug() << "\n=== 测试分页查询Test记录 ===";

    // 清空现有数据并创建足够的测试数据
    clearAllTestData();
    QList<Test> testDataList = createTestData(15); // 创建15条记录用于分页测试

    QEventLoop loop;

    // 批量创建记录
    bool batchCompleted = false;
    m_testRepository->batchCreateTests(testDataList, [&](const AsyncResult<int>& result) {
        batchCompleted = true;
        loop.quit();
    });

    QTimer::singleShot(10000, &loop, &QEventLoop::quit);
    loop.exec();
    QVERIFY(batchCompleted);

    // 测试第一页（前5条记录）
    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;
    QList<Test> firstPageTests;

    m_testRepository->getTestsPaginated(0, 5, [&](const AsyncResult<QList<Test>>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            firstPageTests = result.result;
        } else {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证第一页结果
    QVERIFY2(operationCompleted, "分页查询操作未在超时时间内完成");
    QVERIFY2(success, QString("分页查询操作失败: %1").arg(errorMessage).toLocal8Bit().constData());
    QCOMPARE(firstPageTests.size(), 5);

    // 测试第二页（接下来的5条记录）
    operationCompleted = false;
    QList<Test> secondPageTests;

    m_testRepository->getTestsPaginated(5, 5, [&](const AsyncResult<QList<Test>>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            secondPageTests = result.result;
        } else {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证第二页结果
    QVERIFY(operationCompleted);
    QVERIFY(success);
    QCOMPARE(secondPageTests.size(), 5);

    // 验证两页的记录不重复
    QSet<int> firstPageIds, secondPageIds;
    for (const Test& test : firstPageTests) {
        firstPageIds.insert(test.getId());
    }
    for (const Test& test : secondPageTests) {
        secondPageIds.insert(test.getId());
    }

    QVERIFY(firstPageIds.intersect(secondPageIds).isEmpty()); // 确保没有重复的ID

    qDebug() << "✓ 分页查询Test记录测试通过";
}

void TestTest::testBatchUpdateTests() {
    qDebug() << "\n=== 测试批量更新Test记录 ===";

    // 清空现有数据并创建测试记录
    clearAllTestData();
    QList<Test> testDataList = createTestData(5);

    QEventLoop loop;

    // 批量创建记录
    bool batchCompleted = false;
    m_testRepository->batchCreateTests(testDataList, [&](const AsyncResult<int>& result) {
        batchCompleted = true;
        loop.quit();
    });

    QTimer::singleShot(10000, &loop, &QEventLoop::quit);
    loop.exec();
    QVERIFY(batchCompleted);

    // 获取所有记录以获得它们的ID
    bool getAllCompleted = false;
    QList<Test> allTests;

    m_testRepository->getAllTests([&](const AsyncResult<QList<Test>>& result) {
        getAllCompleted = true;
        if (result.success) {
            allTests = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();
    QVERIFY(getAllCompleted);
    QVERIFY(!allTests.isEmpty());

    // 准备批量更新数据
    QMap<int, Test> updateMap;
    for (int i = 0; i < qMin(3, allTests.size()); ++i) {
        Test updatedTest = allTests[i];
        updatedTest.setName(QString("批量更新%1").arg(i + 1).toStdString());
        updatedTest.setDescription(QString("批量更新的描述%1").arg(i + 1).toStdString());
        updatedTest.setValue((i + 1) * 1000);
        updateMap.insert(updatedTest.getId(), updatedTest);
    }

    // 执行批量更新操作
    bool operationCompleted = false;
    bool success = false;
    QString errorMessage;
    int updatedCount = 0;

    m_testRepository->batchUpdateTests(updateMap, [&](const AsyncResult<int>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            updatedCount = result.result;
        } else {
            errorMessage = result.errorMessage;
        }
        loop.quit();
    });

    QTimer::singleShot(10000, &loop, &QEventLoop::quit);
    loop.exec();

    // 验证批量更新结果
    QVERIFY2(operationCompleted, "批量更新操作未在超时时间内完成");
    QVERIFY2(success, QString("批量更新操作失败: %1").arg(errorMessage).toLocal8Bit().constData());
    QCOMPARE(updatedCount, updateMap.size());

    // 验证更新后的数据
    for (auto it = updateMap.begin(); it != updateMap.end(); ++it) {
        int id = it.key();
        const Test& expectedTest = it.value();

        bool getCompleted = false;
        std::unique_ptr<Test> retrievedTest;

        m_testRepository->getTestById(id, [&](const AsyncResult<std::unique_ptr<Test>>& result) {
            getCompleted = true;
            if (result.success && result.result) {
                retrievedTest = std::make_unique<Test>(*result.result);
            }
            loop.quit();
        });

        QTimer::singleShot(5000, &loop, &QEventLoop::quit);
        loop.exec();

        QVERIFY(getCompleted);
        QVERIFY(retrievedTest != nullptr);
        QCOMPARE(QString::fromStdString(retrievedTest->getName()), QString::fromStdString(expectedTest.getName()));
        QCOMPARE(QString::fromStdString(retrievedTest->getDescription()), QString::fromStdString(expectedTest.getDescription()));
        QCOMPARE(retrievedTest->getValue(), expectedTest.getValue());
    }

    qDebug() << "✓ 批量更新Test记录测试通过，成功更新" << updatedCount << "条记录";
}

// ==================== 错误处理和边界条件测试 ====================

void TestTest::testErrorHandling() {
    qDebug() << "\n=== 测试错误处理和异常情况 ===";

    QEventLoop loop;

    // 测试查询不存在的记录
    bool operationCompleted = false;
    bool success = true; // 初始化为true，期望操作"成功"但返回空结果
    std::unique_ptr<Test> retrievedTest;

    m_testRepository->getTestById(99999, [&](const AsyncResult<std::unique_ptr<Test>>& result) {
        operationCompleted = true;
        success = result.success;
        if (result.success && result.result) {
            retrievedTest = std::make_unique<Test>(*result.result);
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(operationCompleted);
    // 对于不存在的记录，应该成功执行但返回空结果
    QVERIFY(retrievedTest == nullptr);

    // 测试删除不存在的记录
    operationCompleted = false;
    success = false;

    m_testRepository->deleteTest(99999, [&](const AsyncResult<bool>& result) {
        operationCompleted = true;
        success = result.success;
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(operationCompleted);
    // 删除不存在的记录应该仍然返回成功（没有错误发生）
    QVERIFY(success);

    // 测试空的批量操作
    QList<Test> emptyList;
    operationCompleted = false;
    int createdCount = -1;

    m_testRepository->batchCreateTests(emptyList, [&](const AsyncResult<int>& result) {
        operationCompleted = true;
        if (result.success) {
            createdCount = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(operationCompleted);
    QCOMPARE(createdCount, 0); // 空列表应该创建0条记录

    qDebug() << "✓ 错误处理测试通过";
}

void TestTest::testBoundaryConditions() {
    qDebug() << "\n=== 测试边界条件 ===";

    QEventLoop loop;

    // 测试极长的字符串
    QString longName = QString("A").repeated(1000);
    QString longDescription = QString("B").repeated(2000);
    Test longStringTest(longName.toStdString(), longDescription.toStdString(), INT_MAX);

    bool operationCompleted = false;
    bool success = false;
    int createdId = -1;

    m_testRepository->createTest(longStringTest, [&](const AsyncResult<int>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            createdId = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(operationCompleted);
    QVERIFY(success);
    QVERIFY(createdId > 0);

    // 测试空字符串
    Test emptyStringTest("", "", 0);
    operationCompleted = false;
    success = false;
    createdId = -1;

    m_testRepository->createTest(emptyStringTest, [&](const AsyncResult<int>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            createdId = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(operationCompleted);
    QVERIFY(success);
    QVERIFY(createdId > 0);

    // 测试负数值
    Test negativeValueTest("负数测试", "测试负数值", -999);
    operationCompleted = false;
    success = false;
    createdId = -1;

    m_testRepository->createTest(negativeValueTest, [&](const AsyncResult<int>& result) {
        operationCompleted = true;
        success = result.success;
        if (success) {
            createdId = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(operationCompleted);
    QVERIFY(success);
    QVERIFY(createdId > 0);

    // 测试分页的边界条件
    operationCompleted = false;
    QList<Test> paginatedTests;

    // 测试offset超出范围的分页查询
    m_testRepository->getTestsPaginated(1000, 10, [&](const AsyncResult<QList<Test>>& result) {
        operationCompleted = true;
        if (result.success) {
            paginatedTests = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(operationCompleted);
    QVERIFY(paginatedTests.isEmpty()); // 超出范围应该返回空列表

    qDebug() << "✓ 边界条件测试通过";
}

void TestTest::testConcurrentOperations() {
    qDebug() << "\n=== 测试异步操作的并发性 ===";

    // 清空现有数据
    clearAllTestData();

    QEventLoop loop;
    const int concurrentCount = 10;
    QAtomicInt completedOperations(0);
    QAtomicInt successfulOperations(0);

    // 同时发起多个创建操作
    for (int i = 0; i < concurrentCount; ++i) {
        Test concurrentTest(
            QString("并发测试%1").arg(i).toStdString(),
            QString("并发创建的第%1个记录").arg(i).toStdString(),
            i * 100
        );

        m_testRepository->createTest(concurrentTest, [&](const AsyncResult<int>& result) {
            completedOperations.fetchAndAddOrdered(1);
            if (result.success) {
                successfulOperations.fetchAndAddOrdered(1);
            }

            // 当所有操作完成时退出循环
            if (completedOperations.loadAcquire() == concurrentCount) {
                loop.quit();
            }
        });
    }

    // 等待所有操作完成
    QTimer::singleShot(15000, &loop, &QEventLoop::quit); // 15秒超时
    loop.exec();

    // 验证并发操作结果
    QCOMPARE(completedOperations.loadAcquire(), concurrentCount);
    QCOMPARE(successfulOperations.loadAcquire(), concurrentCount);

    // 验证数据库中的记录数
    bool countCompleted = false;
    int totalCount = 0;

    m_testRepository->getTestCount([&](const AsyncResult<int>& result) {
        countCompleted = true;
        if (result.success) {
            totalCount = result.result;
        }
        loop.quit();
    });

    QTimer::singleShot(5000, &loop, &QEventLoop::quit);
    loop.exec();

    QVERIFY(countCompleted);
    QCOMPARE(totalCount, concurrentCount);

    qDebug() << "✓ 并发操作测试通过，成功完成" << successfulOperations.loadAcquire() << "个并发操作";
}

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);

    TESTLIB_SELFCOVERAGE_START(TestTest)
    QT_PREPEND_NAMESPACE(QTest::Internal::callInitMain)<TestTest>();

    TestTest testTest;
    QTEST_SET_MAIN_SOURCE_PATH

        int result = QTest::qExec(&testTest, argc, argv);

    return result;
}

#include "tst_test.moc"
