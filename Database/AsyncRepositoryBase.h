#ifndef ASYNCREPOSITORYBASE_H
#define ASYNCREPOSITORYBASE_H

#include <QObject>
#include <QMutex>
#include <QMutexLocker>
#include <QVariant>
#include <QMetaType>
#include <memory>
#include <functional>
#include <type_traits>
#include "DatabaseManager.h"
#include "DatabaseWorker.h"

// 前向声明
namespace Poco {
    namespace Data {
        class Session;
    }
}

/**
 * @brief 异步操作结果包装器
 *
 * 用于在异步操作中传递结果和错误信息
 */
template<typename T>
struct AsyncResult {
    T result;
    bool success;
    QString errorMessage;

    AsyncResult() : success(false) {
    }

    AsyncResult(const T &res) : result(res), success(true) {
    }

    AsyncResult(const QString &error) : success(false), errorMessage(error) {
    }
};

/**
 * @brief 异步数据库操作基类
 *
 * 提供通用的异步数据库操作功能，消除重复代码
 * 所有具体的Repository类都应该继承此基类
 * 不再依赖模板参数，直接在lambda中处理数据库操作
 */
class AsyncRepositoryBase : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param dbManager 数据库管理器指针
     * @param repositoryName Repository名称（用于日志和调试）
     * @param parent 父对象
     */
    explicit AsyncRepositoryBase(DatabaseManager *dbManager,
                                 const QString &repositoryName,
                                 QObject *parent = nullptr);

    virtual ~AsyncRepositoryBase();

    /**
     * @brief 启动后台工作线程
     */
    void start();

    /**
     * @brief 停止后台工作线程
     */
    void stop();

    /**
     * @brief 检查Repository是否可用
     * @return 可用返回true，不可用返回false
     */
    bool isValid() const;

    /**
     * @brief 检查工作线程是否正在运行
     * @return 运行中返回true
     */
    bool isRunning() const;

    /**
     * @brief 获取待处理任务数量
     * @return 任务数量
     */
    int pendingTaskCount() const;

protected:
    /**
     * @brief 执行异步操作的通用模板方法
     * @tparam ReturnType 操作返回类型
     * @param operation 要执行的操作函数，接受DatabaseManager*参数
     * @param callback 完成回调函数
     * @param operationName 操作名称（用于日志）
     */
    template<typename ReturnType>
    void executeAsync(std::function<ReturnType()> operation,
                      std::function<void(const AsyncResult<ReturnType> &)> callback,
                      const QString &operationName) {
        if (!m_worker) {
            if (callback) {
                AsyncResult<ReturnType> result(QString("%1: Worker not available").arg(m_repositoryName));
                callback(result);
            }
            return;
        }

        // 创建共享的结果存储
        auto resultPtr = std::make_shared<AsyncResult<ReturnType> >();

        auto asyncOperation = [this, operation, resultPtr, operationName]() {
            try {
                resultPtr->result = operation();
                resultPtr->success = true;
            } catch (const std::exception &ex) {
                resultPtr->success = false;
                resultPtr->errorMessage = QString("%1: %2 failed - %3")
                        .arg(m_repositoryName, operationName, ex.what());
            }
        };

        auto asyncCallback = [callback, resultPtr]() {
            if (callback) {
                callback(*resultPtr);
            }
        };

        m_worker->addTask(asyncOperation, asyncCallback,
                          QString("%1_%2").arg(m_repositoryName, operationName));
    }

    /**
     * @brief 执行同步操作的通用模板方法（线程安全）
     * @tparam ReturnType 操作返回类型
     * @param operation 要执行的操作函数
     * @param defaultValue 失败时的默认返回值
     * @return 操作结果
     */
    template<typename ReturnType>
    ReturnType executeSync(std::function<ReturnType()> operation,
                           const ReturnType &defaultValue = ReturnType{}) {
        QMutexLocker locker(&m_mutex);

        try {
            return operation();
        } catch (const std::exception &ex) {
            qWarning() << m_repositoryName << ": Sync operation failed:" << ex.what();
            return defaultValue;
        }
    }

    /**
     * @brief 获取线程安全的数据库会话
     * @return 会话指针
     */
    std::unique_ptr<Poco::Data::Session> getThreadSafeSession();

    /**
     * @brief 获取Repository名称
     * @return Repository名称
     */
    const QString &getRepositoryName() const;

signals:
    /**
     * @brief 操作完成信号
     * @param operation 操作名称
     * @param success 是否成功
     */
    void operationCompleted(const QString &operation, bool success);

    /**
     * @brief 错误信号
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);

private slots:
    /**
     * @brief 处理工作线程任务完成
     * @param description 任务描述
     * @param success 是否成功
     */
    void onTaskCompleted(const QString &description, bool success);

    /**
     * @brief 处理工作线程错误
     * @param error 错误信息
     */
    void onWorkerError(const QString &error);

private:
    DatabaseManager *m_dbManager;
    QString m_repositoryName;
    DatabaseWorker *m_worker;
    mutable QMutex m_mutex;
};

#endif // ASYNCREPOSITORYBASE_H
