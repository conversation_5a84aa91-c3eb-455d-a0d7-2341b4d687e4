# 数据库密码加密快速入门

## 概述

现在DatabaseManager支持自动生成随机密码、使用PocoCrypto加密、并通过QtKeychain安全存储。

## 核心工作流程

### 1. 新数据库（自动密码生成）

```cpp
DatabaseManager *dbManager = new DatabaseManager();

// 自动生成密码、加密并存储
bool result = dbManager->initialize("myapp.db", true, 10,
    [](const QString &generatedPassword) {
        qDebug() << "密码已生成并安全存储";
        // 密码自动加密存储，无需手动处理
    });
```

**发生了什么：**
1. 生成32字符随机密码
2. 生成32字符加密密钥
3. 使用AES-256-CBC加密密码
4. 加密密钥存储到系统密钥链（QtKeychain）
5. 加密密码存储到配置文件
6. 使用明文密码初始化数据库

### 2. 现有数据库（自动密码检索）

```cpp
DatabaseManager *dbManager = new DatabaseManager();

// 自动检索、解密并连接
dbManager->initializeWithStoredPassword("myapp.db", 10,
    [](bool success, const QString &error) {
        if (success) {
            qDebug() << "数据库连接成功";
        } else {
            qDebug() << "连接失败:" << error;
        }
    });
```

**发生了什么：**
1. 从配置文件读取加密密码
2. 从系统密钥链检索加密密钥
3. 解密密码
4. 使用解密的密码连接数据库

## 存储位置

### 加密密钥存储
- **位置**: 系统密钥链
- **服务名**: "TestFindGame"
- **密钥名**: "db_key_[数据库名]"
- **内容**: 32字符AES加密密钥

### 加密密码存储
- **位置**: 应用配置目录
- **文件**: `database_passwords.ini`
- **格式**: INI文件，键为数据库绝对路径
- **内容**: Base64编码的加密密码

## 安全特性

1. **密钥分离**: 加密密钥和加密密码分别存储
2. **系统集成**: 利用操作系统密钥链服务
3. **强加密**: AES-256-CBC对称加密
4. **随机性**: 使用PocoCrypto安全随机数生成器

## 实际应用示例

```cpp
class MyApp : public QObject {
public:
    void initializeDatabase() {
        QString dbPath = "myapp.db";
        
        // 检查是否有存储的密码
        QString encryptedPassword = DatabaseManager::retrieveEncryptedPassword(dbPath);
        
        if (encryptedPassword.isEmpty()) {
            // 新数据库 - 自动生成密码
            createNewDatabase(dbPath);
        } else {
            // 现有数据库 - 使用存储的密码
            connectToExistingDatabase(dbPath);
        }
    }
    
private:
    void createNewDatabase(const QString &dbPath) {
        m_dbManager = new DatabaseManager();
        m_dbManager->initialize(dbPath, true, 10, [this](const QString &password) {
            qDebug() << "新数据库创建完成，密码已安全存储";
            setupApplication();
        });
    }
    
    void connectToExistingDatabase(const QString &dbPath) {
        m_dbManager = new DatabaseManager();
        m_dbManager->initializeWithStoredPassword(dbPath, 10, [this](bool success, const QString &error) {
            if (success) {
                qDebug() << "成功连接到现有数据库";
                setupApplication();
            } else {
                qDebug() << "连接失败，可能需要重新创建数据库";
            }
        });
    }
    
    void setupApplication() {
        // 创建表、初始化Repository等
        m_dbManager->createTables();
        // ... 其他初始化代码
    }
    
private:
    DatabaseManager *m_dbManager;
};
```

## 错误处理

常见错误情况：

1. **密钥链访问被拒绝**: 用户拒绝访问系统密钥链
2. **配置文件损坏**: 加密密码配置文件被删除或损坏
3. **密钥不匹配**: 加密密钥与加密密码不匹配
4. **权限问题**: 配置目录无写入权限

建议的错误恢复策略：
- 提供重新生成密码的选项
- 备份重要数据
- 提供手动密码输入的后备方案

## 注意事项

1. **异步操作**: 所有密钥链操作都是异步的
2. **线程安全**: DatabaseManager本身是线程安全的
3. **密码强度**: 默认生成32字符强密码
4. **兼容性**: 需要Qt6和现代操作系统的密钥链支持

## 示例代码

完整示例请参考：
- `Database/examples/CompleteWorkflowExample.cpp` - 完整工作流程
- `Database/examples/PasswordEncryptionExample.cpp` - 基础功能演示
- `Database/examples/SecureDatabaseExample.cpp` - 实际应用场景
