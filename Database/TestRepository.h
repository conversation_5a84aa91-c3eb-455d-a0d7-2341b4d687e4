#ifndef TESTREPOSITORY_H
#define TESTREPOSITORY_H

#include <QObject>
#include <QList>
#include <memory>
#include <functional>
#include "AsyncRepositoryBase.h"

// 前向声明
class Test;
class DatabaseManager;

namespace Poco {
    namespace Data {
        class Session;
    }
}

/**
 * @brief Test表数据访问层
 *
 * 继承自AsyncRepositoryBase，提供线程安全的异步CRUD操作
 * 使用 POCO ORM 进行数据库操作
 * 所有方法内部使用executeAsync包装器实现线程安全的异步操作
 */
class TestRepository : public AsyncRepositoryBase {
    Q_OBJECT

public:
    /**
     * @brief 构造函数（仅支持异步操作）
     * @param dbManager 数据库管理器指针
     * @param parent 父对象
     */
    explicit TestRepository(DatabaseManager *dbManager, QObject *parent = nullptr);

    ~TestRepository();

    /**
     * @brief 创建Test记录（异步线程安全）
     * @param test Test对象
     * @param callback 完成回调，参数为AsyncResult<int>
     */
    void createTest(const Test &test, std::function<void(const AsyncResult<int> &)> callback = nullptr);

    /**
     * @brief 根据ID获取Test记录（异步线程安全）
     * @param id 记录ID
     * @param callback 完成回调，参数为AsyncResult<std::unique_ptr<Test>>
     */
    void getTestById(int id, std::function<void(const AsyncResult<std::unique_ptr<Test> > &)> callback = nullptr);

    /**
     * @brief 获取所有Test记录（异步线程安全）
     * @param callback 完成回调，参数为AsyncResult<QList<Test>>
     */
    void getAllTests(std::function<void(const AsyncResult<QList<Test> > &)> callback = nullptr);

    /**
     * @brief 根据名称查找Test记录（异步线程安全）
     * @param name 名称
     * @param callback 完成回调，参数为AsyncResult<QList<Test>>
     */
    void getTestsByName(const QString &name, std::function<void(const AsyncResult<QList<Test> > &)> callback = nullptr);

    /**
     * @brief 更新Test记录（异步线程安全）
     * @param id 记录ID
     * @param name 新名称
     * @param description 新描述
     * @param value 新数值
     * @param callback 完成回调，参数为AsyncResult<bool>
     */
    void updateTest(int id, const QString &name, const QString &description, int value,
                    std::function<void(const AsyncResult<bool> &)> callback = nullptr);

    /**
     * @brief 删除Test记录（异步线程安全）
     * @param id 记录ID
     * @param callback 完成回调，参数为AsyncResult<bool>
     */
    void deleteTest(int id, std::function<void(const AsyncResult<bool> &)> callback = nullptr);

    /**
     * @brief 删除所有Test记录（异步线程安全）
     * @param callback 完成回调，参数为AsyncResult<bool>
     */
    void deleteAllTests(std::function<void(const AsyncResult<bool> &)> callback = nullptr);

    /**
     * @brief 获取Test记录总数（异步线程安全）
     * @param callback 完成回调，参数为AsyncResult<int>
     */
    void getTestCount(std::function<void(const AsyncResult<int> &)> callback = nullptr);

    /**
     * @brief 检查Repository是否可用
     * @return 可用返回true，不可用返回false
     */
    bool isValid() const;

    // ==================== 高级ORM特性 ====================

    /**
     * @brief 批量创建Test记录（异步线程安全）
     * @param tests Test对象列表
     * @param callback 完成回调，参数为AsyncResult<int>
     */
    void batchCreateTests(const QList<Test> &tests, std::function<void(const AsyncResult<int> &)> callback = nullptr);

    /**
     * @brief 获取分页的Test记录（异步线程安全）
     * @param offset 偏移量
     * @param limit 限制数量
     * @param callback 完成回调，参数为AsyncResult<QList<Test>>
     */
    void getTestsPaginated(int offset, int limit,
                           std::function<void(const AsyncResult<QList<Test> > &)> callback = nullptr);

    /**
     * @brief 使用事务批量更新Test记录（异步线程安全）
     * @param updates 更新数据的映射 (ID -> Test对象)
     * @param callback 完成回调，参数为AsyncResult<int>
     */
    void batchUpdateTests(const QMap<int, Test> &updates,
                          std::function<void(const AsyncResult<int> &)> callback = nullptr);
};

#endif // TESTREPOSITORY_H
