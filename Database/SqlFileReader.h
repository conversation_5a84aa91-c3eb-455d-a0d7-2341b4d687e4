#ifndef SQLFILEREADER_H
#define SQLFILEREADER_H

#include <QStringList>

/**
 * @brief SQL文件读取器类
 * 
 * 提供读取.sql文件内容的功能，支持解析多条SQL语句
 */
class SqlFileReader {
public:
    SqlFileReader();

    ~SqlFileReader();

    /**
     * @brief 读取SQL文件内容
     * @param filePath SQL文件路径
     * @return 文件内容字符串，失败返回空字符串
     */
    static QString readSqlFile(const QString &filePath);

    /**
     * @brief 读取SQL文件并解析为语句列表
     * @param filePath SQL文件路径
     * @return SQL语句列表，每个元素是一条完整的SQL语句
     */
    static QStringList readSqlStatements(const QString &filePath);

    /**
     * @brief 从资源文件中读取SQL内容
     * @param resourcePath 资源文件路径（如 ":/sql/test.sql"）
     * @return 文件内容字符串，失败返回空字符串
     */
    static QString readSqlFromResource(const QString &resourcePath);

    /**
     * @brief 从资源文件中读取SQL并解析为语句列表
     * @param resourcePath 资源文件路径
     * @return SQL语句列表
     */
    static QStringList readSqlStatementsFromResource(const QString &resourcePath);

private:
    /**
     * @brief 解析SQL内容为语句列表
     * @param sqlContent SQL文件内容
     * @return 解析后的SQL语句列表
     */
    static QStringList parseSqlStatements(const QString &sqlContent);

    /**
     * @brief 清理SQL语句（移除注释和多余空白）
     * @param statement 原始SQL语句
     * @return 清理后的SQL语句
     */
    static QString cleanSqlStatement(const QString &statement);
};

#endif // SQLFILEREADER_H
