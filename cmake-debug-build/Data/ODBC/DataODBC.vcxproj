<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{AF368602-7E7F-36E3-B60D-9B76656797FC}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>DataODBC</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">DataODBC.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PocoDataODBCd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">DataODBC.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PocoDataODBC</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">DataODBC.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PocoDataODBC</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">DataODBC.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PocoDataODBC</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files (x86)/Windows Kits/10/Include/10.0.22621.0/um" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;THREADSAFE;POCO_DLL;SQLParser_EXPORTS;UTF8PROC_STATIC;POCO_CMAKE;_DEBUG;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="Debug";ODBC_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;THREADSAFE;POCO_DLL;SQLParser_EXPORTS;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\";ODBC_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\lib\PocoDatad.lib;odbc32.lib;..\..\lib\PocoFoundationd.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoDataODBCd.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoDataODBCd.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files (x86)/Windows Kits/10/Include/10.0.22621.0/um" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;THREADSAFE;POCO_DLL;SQLParser_EXPORTS;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="Release";ODBC_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;THREADSAFE;POCO_DLL;SQLParser_EXPORTS;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\";ODBC_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\lib\PocoData.lib;odbc32.lib;..\..\lib\PocoFoundation.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoDataODBC.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoDataODBC.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files (x86)/Windows Kits/10/Include/10.0.22621.0/um" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;THREADSAFE;POCO_DLL;SQLParser_EXPORTS;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="MinSizeRel";ODBC_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;THREADSAFE;POCO_DLL;SQLParser_EXPORTS;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"MinSizeRel\";ODBC_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\lib\PocoData.lib;odbc32.lib;..\..\lib\PocoFoundation.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoDataODBC.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoDataODBC.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files (x86)/Windows Kits/10/Include/10.0.22621.0/um" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;THREADSAFE;POCO_DLL;SQLParser_EXPORTS;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="RelWithDebInfo";ODBC_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;THREADSAFE;POCO_DLL;SQLParser_EXPORTS;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"RelWithDebInfo\";ODBC_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\lib\PocoData.lib;odbc32.lib;..\..\lib\PocoFoundation.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoDataODBC.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoDataODBC.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Data/ODBC/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Data/ODBC/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\cmake\PocoDataODBCConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Data\ODBC\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Data/ODBC/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Data/ODBC/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\cmake\PocoDataODBCConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Data\ODBC\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Data/ODBC/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Data/ODBC/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\cmake\PocoDataODBCConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Data\ODBC\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Data/ODBC/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Data/ODBC/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\cmake\PocoDataODBCConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Data\ODBC\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Binder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\ConnectionHandle.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Connector.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Diagnostics.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\EnvironmentHandle.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Error.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Extractor.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\ODBCException.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\ODBCMetaColumn.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\ODBCStatementImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Parameter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Preparator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\SessionImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\TypeInfo.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Unicode.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Unicode_UNIXODBC.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Unicode_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\src\Utility.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Binder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\ConnectionHandle.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Connector.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Diagnostics.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\EnvironmentHandle.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Error.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Extractor.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Handle.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\ODBC.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\ODBCException.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\ODBCMetaColumn.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\ODBCStatementImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Parameter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Preparator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\SessionImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\TypeInfo.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Unicode.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Unicode_UNIXODBC.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Unicode_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\ODBC\include\Poco\Data\ODBC\Utility.h" />
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\DLLVersion.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\ZERO_CHECK.vcxproj">
      <Project>{9633D67F-878A-3B03-AECD-8F1AE98F930E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Data\Data.vcxproj">
      <Project>{69D2DD74-F1FF-33D0-9A8A-AD91324023BF}</Project>
      <Name>Data</Name>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\Foundation.vcxproj">
      <Project>{8A0B40BF-1AF7-39B7-B8FF-092865984EED}</Project>
      <Name>Foundation</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>