<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{69D2DD74-F1FF-33D0-9A8A-AD91324023BF}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Data</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Data.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PocoDatad</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Data.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PocoData</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Data.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PocoData</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Data.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PocoData</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;SQLParser_EXPORTS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;_DEBUG;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="Debug";Data_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;SQLParser_EXPORTS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\";Data_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoFoundationd.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoDatad.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoDatad.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SQLParser_EXPORTS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="Release";Data_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SQLParser_EXPORTS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\";Data_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoFoundation.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoData.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoData.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SQLParser_EXPORTS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="MinSizeRel";Data_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SQLParser_EXPORTS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"MinSizeRel\";Data_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoFoundation.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoData.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoData.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SQLParser_EXPORTS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="RelWithDebInfo";Data_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SQLParser_EXPORTS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"RelWithDebInfo\";Data_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Data\include;D:\CWorkspace\poco-poco-1.14.2-release\Data\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src;D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoFoundation.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoData.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoData.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Data/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Data/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Data\cmake\PocoDataConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Data\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Data/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Data/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Data\cmake\PocoDataConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Data\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Data/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Data/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Data\cmake\PocoDataConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Data\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Data/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Data/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Data\cmake\PocoDataConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Data\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\AbstractBinder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\AbstractBinding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\AbstractExtraction.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\AbstractExtractor.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\AbstractPreparation.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\AbstractPreparator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\ArchiveStrategy.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Bulk.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Connector.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\DataException.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Date.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\DynamicLOB.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\JSONRowFormatter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Limit.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\MetaColumn.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\PooledSessionHolder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\PooledSessionImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Position.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Range.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\RecordSet.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Row.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\RowFilter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\RowFormatter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\RowIterator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\SQLChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Session.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\SessionFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\SessionImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\SessionPool.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\SessionPoolContainer.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\SimpleRowFormatter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Statement.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\StatementCreator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\StatementImpl.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalOptions) /bigobj</AdditionalOptions>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalOptions) /bigobj</AdditionalOptions>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalOptions) /bigobj</AdditionalOptions>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalOptions) /bigobj</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Time.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Transaction.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\src\Transcoder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\SQLParser.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\SQLParserResult.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\parser\bison_parser.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\parser\flex_lexer.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\sql\CreateStatement.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\sql\Expr.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\sql\PrepareStatement.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\sql\SQLStatement.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\sql\statements.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\SQLParser\src\util\sqlhelper.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\AbstractBinder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\AbstractBinding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\AbstractExtraction.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\AbstractExtractor.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\AbstractPreparation.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\AbstractPreparator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\AbstractSessionImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\ArchiveStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\AutoTransaction.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Binding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Bulk.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\BulkBinding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\BulkExtraction.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Column.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Connector.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Constants.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Data.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\DataException.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Date.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\DynamicDateTime.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\DynamicLOB.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Extraction.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\JSONRowFormatter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\LOB.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\LOBStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Limit.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\MetaColumn.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\PooledSessionHolder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\PooledSessionImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Position.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Preparation.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Range.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\RecordSet.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Row.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\RowFilter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\RowFormatter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\RowIterator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\SQLChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Session.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\SessionFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\SessionImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\SessionPool.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\SessionPoolContainer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\SimpleRowFormatter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Statement.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\StatementCreator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\StatementImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Time.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Transaction.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\Transcoder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Data\include\Poco\Data\TypeHandler.h" />
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\DLLVersion.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\ZERO_CHECK.vcxproj">
      <Project>{9633D67F-878A-3B03-AECD-8F1AE98F930E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\Foundation.vcxproj">
      <Project>{8A0B40BF-1AF7-39B7-B8FF-092865984EED}</Project>
      <Name>Foundation</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>