<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Collector.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Counter.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Gauge.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Histogram.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\IntCounter.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\IntGauge.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\LabeledMetric.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\MetricsRequestHandler.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\MetricsServer.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\ProcessCollector.cpp">
      <Filter>Collectors\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Registry.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\TextExporter.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\ThreadPoolCollector.cpp">
      <Filter>Collectors\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\AtomicFloat.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\CallbackMetric.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Collector.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Counter.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Exporter.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Gauge.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Histogram.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\IntCounter.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\IntGauge.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\LabeledMetric.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\LabeledMetricImpl.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Metric.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\MetricsRequestHandler.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\MetricsServer.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\ProcessCollector.h">
      <Filter>Collectors\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Prometheus.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Registry.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\TextExporter.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\ThreadPoolCollector.h">
      <Filter>Collectors\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\DLLVersion.rc">
      <Filter>Resources</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Collectors">
      <UniqueIdentifier>{FE42D6AC-CB2E-3B3C-B70A-A1F6D8B84C28}</UniqueIdentifier>
    </Filter>
    <Filter Include="Collectors\Header Files">
      <UniqueIdentifier>{C3953FE6-EA48-38E6-A3A5-2E32A9893B96}</UniqueIdentifier>
    </Filter>
    <Filter Include="Collectors\Source Files">
      <UniqueIdentifier>{B2CCCE17-E745-3382-809A-A0C6D329650F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core">
      <UniqueIdentifier>{1BF66DF9-0017-3967-9A1E-F7154224F6A7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Header Files">
      <UniqueIdentifier>{22371C22-4F95-3528-A3C6-E3E446E4A96C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Source Files">
      <UniqueIdentifier>{CD4CEDC9-91A0-3928-A847-88338E455D3F}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP">
      <UniqueIdentifier>{64729F98-C6AC-34B6-AD9A-B04716DBE48E}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Header Files">
      <UniqueIdentifier>{34E1BC8E-28F1-3063-9665-D23311AF65F4}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Source Files">
      <UniqueIdentifier>{B9A59C2C-E306-314D-A71F-98ADB444F1F1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resources">
      <UniqueIdentifier>{4EE0C587-0023-37BD-9239-61AA155C92EE}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
