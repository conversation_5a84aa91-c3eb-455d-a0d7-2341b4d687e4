<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{EE620DD5-5342-3425-AF5D-05AEF6F2230A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Net</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Net.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PocoNetd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Net.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PocoNet</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Net.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PocoNet</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Net.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PocoNet</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;POCO_DLL;POCO_HAVE_SENDFILE;UTF8PROC_STATIC;POCO_CMAKE;_DEBUG;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="Debug";Net_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;POCO_DLL;POCO_HAVE_SENDFILE;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\";Net_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoFoundationd.lib;iphlpapi.lib;ws2_32.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoNetd.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoNetd.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;POCO_HAVE_SENDFILE;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="Release";Net_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;POCO_HAVE_SENDFILE;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\";Net_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoFoundation.lib;iphlpapi.lib;ws2_32.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoNet.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoNet.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;POCO_HAVE_SENDFILE;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="MinSizeRel";Net_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;POCO_HAVE_SENDFILE;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"MinSizeRel\";Net_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoFoundation.lib;iphlpapi.lib;ws2_32.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoNet.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoNet.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;POCO_HAVE_SENDFILE;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="RelWithDebInfo";Net_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;POCO_HAVE_SENDFILE;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"RelWithDebInfo\";Net_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Net\src;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoFoundation.lib;iphlpapi.lib;ws2_32.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoNet.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoNet.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Net/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Net/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Net\cmake\PocoNetConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Net\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Net/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Net/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Net\cmake\PocoNetConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Net\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Net/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Net/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Net\cmake\PocoNetConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Net\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Net/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Net/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Net\cmake\PocoNetConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Net\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\AbstractHTTPRequestHandler.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\DNS.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\DatagramSocket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\DatagramSocketImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\DialogSocket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\EscapeHTMLStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\FTPClientSession.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\FTPStreamFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\FilePartSource.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTMLForm.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPAuthenticationParams.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPBasicCredentials.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPBufferAllocator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPChunkedStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPClientSession.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPCookie.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPCredentials.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPDigestCredentials.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPFixedLengthStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPHeaderStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPIOStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPMessage.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPNTLMCredentials.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPRequest.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPRequestHandler.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPRequestHandlerFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPResponse.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServer.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerConnection.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerConnectionFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerParams.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerRequest.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerRequestImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerResponse.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerResponseImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerSession.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPSession.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPSessionFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPSessionInstantiator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPStreamFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HostEntry.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPClient.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPEventArgs.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPPacket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPPacketImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPSocket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPSocketImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPv4PacketImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\IPAddress.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\IPAddressImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MailMessage.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MailRecipient.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MailStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MediaType.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MessageHeader.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MulticastSocket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MultipartReader.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MultipartWriter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NTLMCredentials.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NTPClient.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NTPEventArgs.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NTPPacket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NameValueCollection.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\Net.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NetException.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NetworkInterface.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NullPartHandler.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\OAuth10Credentials.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\OAuth20Credentials.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\POP3ClientSession.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\PartHandler.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\PartSource.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\PartStore.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\PollSet.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\QuotedPrintableDecoder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\QuotedPrintableEncoder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\RawSocket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\RawSocketImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\RemoteSyslogChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\RemoteSyslogListener.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SMTPChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SMTPClientSession.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SSPINTLMCredentials.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ServerSocket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ServerSocketImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\Socket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketAddress.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketAddressImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketNotification.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketNotifier.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketProactor.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketReactor.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\StreamSocket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\StreamSocketImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\StringPartSource.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServer.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServerConnection.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServerConnectionFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServerDispatcher.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServerParams.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\UDPClient.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\UDPServerParams.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\WebSocket.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\WebSocketImpl.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\AbstractHTTPRequestHandler.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\DNS.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\DatagramSocket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\DatagramSocketImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\DialogSocket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\EscapeHTMLStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\FTPClientSession.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\FTPStreamFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\FilePartSource.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTMLForm.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPAuthenticationParams.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPBasicCredentials.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPBasicStreamBuf.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPBufferAllocator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPChunkedStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPClientSession.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPCookie.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPCredentials.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPDigestCredentials.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPFixedLengthStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPHeaderStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPIOStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPMessage.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPNTLMCredentials.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPRequest.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPRequestHandler.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPRequestHandlerFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPResponse.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerConnection.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerConnectionFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerParams.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerRequest.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerRequestImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerResponse.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerResponseImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerSession.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPSession.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPSessionFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPSessionInstantiator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPStreamFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HostEntry.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPClient.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPEventArgs.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPPacket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPPacketImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPSocket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPSocketImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPv4PacketImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\IPAddress.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\IPAddressImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MailMessage.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MailRecipient.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MailStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MediaType.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MessageHeader.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MultiSocketPoller.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MulticastSocket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MultipartReader.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MultipartWriter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NTLMCredentials.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NTPClient.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NTPEventArgs.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NTPPacket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NameValueCollection.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\Net.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NetException.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NetworkInterface.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NullPartHandler.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\OAuth10Credentials.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\OAuth20Credentials.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\POP3ClientSession.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ParallelSocketAcceptor.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ParallelSocketReactor.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\PartHandler.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\PartSource.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\PartStore.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\PollSet.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\QuotedPrintableDecoder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\QuotedPrintableEncoder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\RawSocket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\RawSocketImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\RemoteSyslogChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\RemoteSyslogListener.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SMTPChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SMTPClientSession.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SSPINTLMCredentials.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ServerSocket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ServerSocketImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SingleSocketPoller.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\Socket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketAcceptor.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketAddress.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketAddressImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketConnector.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketDefs.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketNotification.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketNotifier.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketProactor.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketReactor.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\StreamSocket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\StreamSocketImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\StringPartSource.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServerConnection.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServerConnectionFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServerDispatcher.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServerParams.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPClient.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPHandler.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPServer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPServerParams.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPSocketReader.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\WebSocket.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\WebSocketImpl.h" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\wepoll.c" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\wepoll.h" />
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\DLLVersion.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\ZERO_CHECK.vcxproj">
      <Project>{9633D67F-878A-3B03-AECD-8F1AE98F930E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\Foundation.vcxproj">
      <Project>{8A0B40BF-1AF7-39B7-B8FF-092865984EED}</Project>
      <Name>Foundation</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>