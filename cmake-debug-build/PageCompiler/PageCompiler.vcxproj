<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{163605F8-A4AD-3707-A49C-1DA6DBD78B78}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>PageCompiler</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PageCompiler.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cpspc</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PageCompiler.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cpspc</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PageCompiler.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">cpspc</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PageCompiler.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">cpspc</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;_DEBUG;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;XML_STATIC;XML_DTD;XML_GE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;XML_STATIC;XML_DTD;XML_GE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoNetd.lib;..\lib\PocoUtild.lib;iphlpapi.lib;ws2_32.lib;..\lib\PocoXMLd.lib;..\lib\PocoJSONd.lib;..\lib\PocoFoundationd.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/cpspc.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/cpspc.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;XML_STATIC;XML_DTD;XML_GE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;XML_STATIC;XML_DTD;XML_GE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoNet.lib;..\lib\PocoUtil.lib;iphlpapi.lib;ws2_32.lib;..\lib\PocoXML.lib;..\lib\PocoJSON.lib;..\lib\PocoFoundation.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/cpspc.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/cpspc.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;XML_STATIC;XML_DTD;XML_GE;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;XML_STATIC;XML_DTD;XML_GE;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoNet.lib;..\lib\PocoUtil.lib;iphlpapi.lib;ws2_32.lib;..\lib\PocoXML.lib;..\lib\PocoJSON.lib;..\lib\PocoFoundation.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/cpspc.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/cpspc.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;XML_STATIC;XML_DTD;XML_GE;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;XML_STATIC;XML_DTD;XML_GE;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Util\include;D:\CWorkspace\poco-poco-1.14.2-release\XML\include;D:\CWorkspace\poco-poco-1.14.2-release\JSON\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoNet.lib;..\lib\PocoUtil.lib;iphlpapi.lib;ws2_32.lib;..\lib\PocoXML.lib;..\lib\PocoJSON.lib;..\lib\PocoFoundation.lib;iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/cpspc.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/cpspc.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\PageCompiler\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/PageCompiler/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/PageCompiler/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\PageCompiler\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/PageCompiler/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/PageCompiler/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\PageCompiler\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/PageCompiler/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/PageCompiler/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\PageCompiler\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/PageCompiler/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/PageCompiler/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\PageCompiler\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\PageCompiler\src\ApacheCodeWriter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\PageCompiler\src\CodeWriter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\PageCompiler\src\OSPCodeWriter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\PageCompiler\src\Page.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\PageCompiler\src\PageCompiler.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\PageCompiler\src\PageReader.cpp" />
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\DLLVersion.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\ZERO_CHECK.vcxproj">
      <Project>{9633D67F-878A-3B03-AECD-8F1AE98F930E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\Foundation.vcxproj">
      <Project>{8A0B40BF-1AF7-39B7-B8FF-092865984EED}</Project>
      <Name>Foundation</Name>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\JSON\JSON.vcxproj">
      <Project>{6E89B0E3-6496-34B8-86AF-CE91B891A378}</Project>
      <Name>JSON</Name>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Net\Net.vcxproj">
      <Project>{EE620DD5-5342-3425-AF5D-05AEF6F2230A}</Project>
      <Name>Net</Name>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Util\Util.vcxproj">
      <Project>{2131B13B-953F-3133-B858-C372D04E297E}</Project>
      <Name>Util</Name>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\XML\XML.vcxproj">
      <Project>{5B9649F0-D9BE-3F6B-B61E-4C004B47265F}</Project>
      <Name>XML</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>