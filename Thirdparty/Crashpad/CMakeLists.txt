cmake_minimum_required(VERSION 3.16)

project(Crashpad VERSION 1.0 LANGUAGES CXX)

# 定义 Crashpad 库的路径
set(CRASHPAD_ROOT ${CMAKE_CURRENT_SOURCE_DIR})
set(CRASHPAD_INCLUDE_DIR ${CRASHPAD_ROOT}/Include/crashpad)
set(CRASHPAD_INCLUDE_MINI_CHROMIUM_DIR ${CRASHPAD_INCLUDE_DIR}/third_party/mini_chromium/mini_chromium)
set(CRASHPAD_INCLUDE_GEN_DIR ${CRASHPAD_INCLUDE_DIR}/out/Default/gen)

# 设置 Windows 平台的库路径和二进制文件路径
set(CRASHPAD_LIB_DIR ${CRASHPAD_ROOT}/Libraries/Windows)
set(CRASHPAD_BIN_DIR ${CRASHPAD_ROOT}/Bin/Windows)

# 创建 Crashpad 接口库
add_library(crashpad STATIC
    crashpadwrapper.h
    crashpadwrapper.cpp
    paths.h
    paths.cpp
)

if(Precompiled_Headers)
    # 预编译头文件
    target_precompile_headers(crashpad
        PRIVATE
            # 项目本地头文件
            "crashpadwrapper.h"
            "paths.h"
    )
endif()

# 暴露 crashpad 的头文件目录
target_include_directories(crashpad
    PUBLIC
        ${CRASHPAD_ROOT}
        ${CRASHPAD_INCLUDE_DIR}
        ${CRASHPAD_INCLUDE_MINI_CHROMIUM_DIR}
        ${CRASHPAD_INCLUDE_GEN_DIR}
)

if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    target_compile_options(crashpad PRIVATE -g)
endif()

# 构建变量
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(LIBDIR ${CRASHPAD_LIB_DIR}/MDd)
    set(EXEDIR ${CMAKE_CURRENT_BINARY_DIR}/Debug)
    set(HANDLER_EXE ${CRASHPAD_BIN_DIR}/crashpad_handler.exe)
else()
    set(LIBDIR ${CRASHPAD_LIB_DIR}/MD)
    set(EXEDIR ${CMAKE_CURRENT_BINARY_DIR}/Release)
    set(HANDLER_EXE ${CRASHPAD_BIN_DIR}/crashpad_handler.exe)
endif()

set(CRASHPAD_LIBRARIES
    ${LIBDIR}/base.lib
    ${LIBDIR}/common.lib
    ${LIBDIR}/client.lib
    ${LIBDIR}/util.lib
    Advapi32
)

# Crashpad 库
target_link_libraries(crashpad
    PUBLIC
        Qt6::Core
    PRIVATE
        ${CRASHPAD_LIBRARIES}
)

# 复制 crashpad_handler, attachment.txt 到构建目录，并上传符号
add_custom_command(TARGET crashpad POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_BINARY_DIR}/crashpad"
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${HANDLER_EXE}"
        "${CMAKE_CURRENT_BINARY_DIR}/crashpad/crashpad_handler.exe"
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${CMAKE_CURRENT_SOURCE_DIR}/attachment.txt"
        "${CMAKE_CURRENT_BINARY_DIR}/attachment.txt"
    COMMENT "复制 crashpad_handler 和附件文件到构建目录"
)


